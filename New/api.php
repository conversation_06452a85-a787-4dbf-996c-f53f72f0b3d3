<?php
require_once("auth_functions.php");


function generateRandom($length = 10) {
	$characters = '0123456789abcdef<PERSON>ijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
	$charactersLength = strlen($characters);
	$randomString = '';
	for ($i = 0; $i < $length; $i++) {
		$randomString .= $characters[rand(0, $charactersLength - 1)];
	}
	return $randomString;
}




function allocate_server_for_order($pdo, $order_id) {
  error_log("=== ALLOCATE SERVER FOR ORDER $order_id ===");

  $server_allocated = false;
  $server_id = 0;

  try {
    // Get order details - no transaction needed for read-only
    $order_query = "SELECT * FROM orders WHERE id = $order_id";
    error_log("Order query: $order_query");
    $order_result = $pdo->query($order_query);

    if (!$order_result || $order_result->rowCount() == 0) {
      error_log("Order not found: $order_id");
      return [false, 0];
    }

    $order = $order_result->fetch(PDO::FETCH_ASSOC);
    error_log("Order found: " . json_encode($order));

    // Only proceed for dedicated orders that don't already have a server
    if ($order['order_type'] != 'dedicated' || $order['assigned_server_id'] > 0) {
      error_log("Skipping server allocation: not a dedicated order or already has a server");
      return [false, $order['assigned_server_id']];
    }

    $config_id = $order['config_id'];
    $os_id = $order['os_id'] ?: 1;  // Default to OS ID 1 if not set

    // THIS FUNCTION HANDLES ITS OWN TRANSACTION
    // Start a transaction specifically for the allocation operations
    $pdo->beginTransaction();
    error_log("Started transaction for server allocation");

    // Look for an available server
    $server_check_sql = "
      SELECT s.id
      FROM servers s
      INNER JOIN dedicated_configs dc ON s.config_id = dc.id
      WHERE s.status = 'Available'
      AND dc.id = $config_id
      LIMIT 1
      FOR UPDATE
    ";

    error_log("Server check SQL: $server_check_sql");
    $server_result = $pdo->query($server_check_sql);

    if ($server_result && $server_result->rowCount() > 0) {
      $row = $server_result->fetch(PDO::FETCH_ASSOC);
      $server_id = $row['id'];
      $server_allocated = true;
      error_log("Found server ID: $server_id");

      // Update server status
      $update_server_sql = "
        UPDATE servers
        SET status = 'Allocated',
            order_id = $order_id,
            os_id = $os_id
        WHERE id = $server_id
      ";
      error_log("Update server SQL: $update_server_sql");
      $pdo->exec($update_server_sql);

      // Update order with assigned server and status=Installing
      $update_order_sql = "
        UPDATE orders
        SET status = 'Installing',
            assigned_server_id = $server_id
        WHERE id = $order_id
      ";
      error_log("Update order SQL: $update_order_sql");
      $pdo->exec($update_order_sql);

      error_log("Server allocation successful");
    } else {
      error_log("No available server found for config_id $config_id");
    }

    // Commit the allocation transaction
    $pdo->commit();
    error_log("Committed allocation transaction");

    return [$server_allocated, $server_id];
  } catch (Exception $e) {
    error_log("Error in allocate_server_for_order: " . $e->getMessage());

    // Rollback if there's an active transaction
    if ($pdo->inTransaction()) {
      $pdo->rollBack();
      error_log("Rolled back allocation transaction");
    }

    return [false, 0];
  }
}




if($_GET['f'] == 'manage_dedicated'){
	$user_id = auth_user();
	$dedicated_id = $_GET['id'];
	$sth = $pdo->prepare("SELECT * from `orders` WHERE `owner_id` = :user_id AND order_type='dedicated' AND id=:id");
	$sth->bindValue(':user_id', $user_id);
	$sth->bindValue(':id', $dedicated_id);
	$sth->execute();

	$output = "[";
	$first = 1;

	while($row = $sth->fetch()){
		if($row['assigned_server_type'] == 'blade_node'){
			$sth2 = $pdo->prepare("SELECT * FROM bladeserver_nodes WHERE `id`=:id");
			$sth2->bindValue(':id', $row['assigned_server_id']);
			$sth2->execute();
			$row2 = $sth2->fetch();

			$switch_id = $row2['switch_id'];
			$switch_port = $row2['switch_port'];
			$ipmi_ip = $row2['ipmi_ip'];
			$ipmi_user = $row2['ipmi_user'];
			$ipmi_pass = $row2['ipmi_user_pass'];


			$sth4 = $pdo->prepare("SELECT * FROM bladeservers WHERE `id`=:id");
			$sth4->bindValue(':id', $row2['bladeserver_id']);
			$sth4->execute();
			$row4 = $sth4->fetch();

			$sth5 = $pdo->prepare("SELECT * FROM cabinets WHERE `id`=:id");
			$sth5->bindValue(':id', $row4['cabinet_id']);
			$sth5->execute();
			$row5 = $sth5->fetch();

			$cabinet_name = $row5['name'];

			$sth6 = $pdo->prepare("SELECT * FROM datacenters WHERE `id`=:id");
			$sth6->bindValue(':id', $row5['datacenter_id']);
			$sth6->execute();
			$row6 = $sth6->fetch();

			$datacenter_name = $row6['name'];

		}else{
			$sth2 = $pdo->prepare("SELECT * FROM servers WHERE `id`=:id");
			$sth2->bindValue(':id', $row['assigned_server_id']);
			$sth2->execute();
			$row2 = $sth2->fetch();

			$switch_id = $row2['switch_id'];
			$switch_port = $row2['switch_port'];
			$ipmi_ip = $row2['ipmi_ip'];
			$ipmi_user = $row2['ipmi_user'];
			$ipmi_pass = $row2['ipmi_user_pass'];

			$sth5 = $pdo->prepare("SELECT * FROM cabinets WHERE `id`=:id");
			$sth5->bindValue(':id', $row2['cabinet_id']);
			$sth5->execute();
			$row5 = $sth5->fetch();

			$cabinet_name = $row5['name'];

			$sth6 = $pdo->prepare("SELECT * FROM datacenters WHERE `id`=:id");
			$sth6->bindValue(':id', $row5['datacenter_id']);
			$sth6->execute();
			$row6 = $sth6->fetch();

			$datacenter_name = $row6['name'];
		}

		$subnet_id = $row2['subnet_id'];
		$os_id = $row2['os_id'];
		$config_id = $row['config_id'];


		$sth3 = $pdo->prepare("SELECT * FROM subnets WHERE `id`=:id");
		$sth3->bindValue(':id', $subnet_id);
		$sth3->execute();
		$row3 = $sth3->fetch();

		$subnet = $row3['subnet'];

		$sth3 = $pdo->prepare("SELECT * FROM dedicated_os WHERE `id`=:id");
		$sth3->bindValue(':id', $os_id);
		$sth3->execute();
		$row3 = $sth3->fetch();

		$os = $row3['os_name'];
		$os_logo_url = $row3['logo_url'];

		$sth3 = $pdo->prepare("SELECT * FROM dedicated_configs WHERE `id`=:id");
		$sth3->bindValue(':id', $config_id);
		$sth3->execute();
		$row3 = $sth3->fetch();

		$cpuram_id = $row3['cpuram_id'];
		$storage_id = $row3['storage_id'];
		$bandwidth_id = $row3['bandwidth_id'];
		$country_id = $row3['country_id'];

		$sth3 = $pdo->prepare("SELECT * FROM dedicated_cpuram WHERE `id`=:id");
		$sth3->bindValue(':id', $cpuram_id);
		$sth3->execute();
		$row3 = $sth3->fetch();

		$cpu = $row3['cpu'];
		$ram = $row3['ram'];

		$sth3 = $pdo->prepare("SELECT * FROM dedicated_storages WHERE `id`=:id");
		$sth3->bindValue(':id', $storage_id);
		$sth3->execute();
		$row3 = $sth3->fetch();

		$storage = $row3['name'];

		$sth3 = $pdo->prepare("SELECT * FROM dedicated_bandwidth WHERE `id`=:id");
		$sth3->bindValue(':id', $bandwidth_id);
		$sth3->execute();
		$row3 = $sth3->fetch();

		$bandwidth = $row3['name'];

		$sth3 = $pdo->prepare("SELECT * FROM dedicated_countries WHERE `id`=:id");
		$sth3->bindValue(':id', $country_id);
		$sth3->execute();
		$row3 = $sth3->fetch();

		$location = $row3['short_name'];
		$locationname= $row3['long_name'];

		$sth3 = $pdo->prepare("SELECT * FROM switchports WHERE `switch_id`=:switch_id AND `switch_port`=:switch_port");
		$sth3->bindValue(':switch_id', $switch_id);
		$sth3->bindValue(':switch_port', $switch_port);
		$sth3->execute();
		$row3 = $sth3->fetch();

		$switchport_status = $row3['status'];

		if($first != 1)
			$output = $output.',';
		$output = $output.'
			{
			"id": "'.$row['id'].'",
			"label": "'.$row['label'].'",
			"type": "'.ucfirst($row['order_type']).'",
			"location": "'.$location.'",
			"locationname": "'.$locationname.'",
			"datacenter": "'.$datacenter_name.'",
			"cabinet": "'.$cabinet_name.'",
			"price": "'.$row['recurring_price'].'",
			"expires": "'.explode(' ',$row['expiration_date'])[0].'",
			"status": "'.ucfirst($row['status']).'",
			"server_status": "'.ucfirst($row2['status']).'",
			"switchport": "'.ucfirst($switchport_status).'",
			"subnet": "'.$subnet.'",
			"os": "'.$os.'",
			"os_logo_url": "'.$os_logo_url.'",
			"bandwidth": "'.$bandwidth.'",
			"cpu": "'.$cpu.'",
			"ram": "'.$ram.'",
			"disks": "'.$storage.'",
			"ipmi_ip": "'.$ipmi_ip.'",
			"ipmi_user": "'.$ipmi_user.'",
			"ipmi_pass": "'.$ipmi_pass.'",
			"ipmi_type": "'.$ipmi_type.'"
			}';
		$first++;
	}

	$output = $output .']';
	die($output);

}

elseif($_GET['f'] == 'upgradeable_disk_layouts'){

	$user_id=auth_user();
	$sth = $pdo->prepare("SELECT * FROM orders WHERE `owner_id`=:owner_id AND `id`=:id");
	$sth->bindValue(':id', $_GET['id']);
	$sth->bindValue(':owner_id', $user_id);
	$sth->execute();
	$row = $sth->fetch();

	$config_id = $row['config_id'];
	$price = $row['recurring_price'];
	$discount_rate = $row['discount_rate'];

	$old_price = round($price - $price * $discount_rate / 100);

	$sth = $pdo->prepare("SELECT * FROM dedicated_configs WHERE `id`=:id");
	$sth->bindValue(':id', $config_id);
	$sth->execute();
	$row = $sth->fetch();

	$cpuram_id = $row['cpuram_id'];
	$storage_id = $row['storage_id'];
	$bandwidth_id = $row['bandwidth_id'];
	$country_id = $row['country_id'];

	$sth2 = $pdo->prepare("SELECT * FROM dedicated_configs WHERE `cpuram_id`=:cpuram_id AND `bandwidth_id`=:bandwidth_id AND `country_id`=:country_id AND `storage_id`!=:storage_id AND is_hidden=0");
	$sth2->bindValue(':cpuram_id', $cpuram_id);
	$sth2->bindValue(':storage_id', $storage_id);
	$sth2->bindValue(':bandwidth_id', $bandwidth_id);
	$sth2->bindValue(':country_id', $country_id);
	$sth2->execute();
	$first = 1;
	$output = "[";
	while($row2 = $sth2->fetch()){

		$sth3 = $pdo->prepare("SELECT * FROM dedicated_storages WHERE `id`=:id");
		$sth3->bindValue(':id', $row2['storage_id']);
		$sth3->execute();
		$row3 = $sth3->fetch();
		$new_price = round($row2['price'] - $discount_rate * $row2['price'] / 100);
		$additional_price = $new_price - $old_price;

		if($first != 1)
			$output = $output.',';
		$output = $output.'{"id": "'.$row2['id'].'","name": "'.$row3['name'].'", "price": "'.$additional_price.'"}';
		$first = 0;


	}
	$output = $output."]";
	die($output);

}
elseif($_GET['f'] == 'upgradeable_bandwidth'){

	$user_id=auth_user();
	$sth = $pdo->prepare("SELECT * FROM orders WHERE `owner_id`=:owner_id AND `id`=:id");
	$sth->bindValue(':id', $_GET['id']);
	$sth->bindValue(':owner_id', $user_id);
	$sth->execute();
	$row = $sth->fetch();

	$config_id = $row['config_id'];
	$price = $row['recurring_price'];
	$discount_rate = $row['discount_rate'];

	$old_price = round($price - $price * $discount_rate / 100);

	$sth = $pdo->prepare("SELECT * FROM dedicated_configs WHERE `id`=:id");
	$sth->bindValue(':id', $config_id);
	$sth->execute();
	$row = $sth->fetch();

	$cpuram_id = $row['cpuram_id'];
	$storage_id = $row['storage_id'];
	$bandwidth_id = $row['bandwidth_id'];
	$country_id = $row['country_id'];

	$sth2 = $pdo->prepare("SELECT * FROM dedicated_configs WHERE `cpuram_id`=:cpuram_id AND `country_id`=:country_id AND `storage_id`=:storage_id AND `bandwidth_id`!=:bandwidth_id AND is_hidden=0");
	$sth2->bindValue(':cpuram_id', $cpuram_id);
	$sth2->bindValue(':storage_id', $storage_id);
	$sth2->bindValue(':bandwidth_id', $bandwidth_id);
	$sth2->bindValue(':country_id', $country_id);
	$sth2->execute();
	$first = 1;
	$output = "[";
	while($row2 = $sth2->fetch()){

		$sth3 = $pdo->prepare("SELECT * FROM dedicated_bandwidth WHERE `id`=:id");
		$sth3->bindValue(':id', $row2['bandwidth_id']);
		$sth3->execute();
		$row3 = $sth3->fetch();

		$new_price = round($row2['price'] - $discount_rate * $row2['price'] / 100);

		$additional_price = $new_price - $old_price;

		if($first != 1)
			$output = $output.',';
		$output = $output.'{"id": "'.$row2['id'].'","name": "'.$row3['name'].'", "price": "'.$additional_price.'"}';
		$first = 0;


	}
	$output = $output."]";
	die($output);

}elseif($_GET['f'] == 'upgradeable_subnets'){

	$user_id=auth_user();

	$sth = $pdo->prepare("SELECT * FROM orders WHERE `owner_id`=:owner_id AND `id`=:id");
	$sth->bindValue(':id', $_GET['id']);
	$sth->bindValue(':owner_id', $user_id);
	$sth->execute();
	$row = $sth->fetch();

	$old_subnet_id = $row['subnet_config_id'];

	$sth = $pdo->prepare("SELECT * FROM dedicated_subnets WHERE `id`=:id");
	$sth->bindValue(':id', $old_subnet_id);
	$sth->execute();
	$row = $sth->fetch();

	$old_subnet_price = $row['price'];

	$sth = $pdo->prepare("SELECT * FROM dedicated_subnets");
	$sth->execute();

	$first = 1;
	$output = "[";
	while($row = $sth->fetch()){
		if($row['id'] == $old_subnet_id){
			$name = "Replace the current ".$row['name']." subnet";
			$price = 25;
			$is_recurring = 0;
		}else{
			$name = $row['name'];
			$price = $row['price'] - $old_subnet_price;
			$is_recurring = 1;
		}
		if($first != 1)
			$output = $output.',';

		$output = $output.'{"id": "'.$row['id'].'","name": "'.$name.'", "price": "'.$price.'", "is_recurring": "'.$is_recurring.'"}';
		$first = 0;
	}
	$output = $output."]";
	die($output);

}elseif($_GET['f'] == 'reinstallable_os'){

	$user_id=auth_user();

	$sth = $pdo->prepare("SELECT * FROM dedicated_os");
	$sth->execute();

	$first = 1;
	$output = "[";

	while($row = $sth->fetch()){
		$name = $row['name'];

		if($first != 1)
			$output = $output.',';

		$output = $output.'{"id": "'.$row['id'].'","name": "'.$row['os_name'].'"}';
		$first = 0;
	}
	$output = $output."]";
	die($output);

}

// Replace the placededicatedorder function in api.php with this fixed version

// PART 1: Modified placededicatedorder function - NO server allocation yet
elseif($_GET['f'] == 'placededicatedorder'){
  error_log("=== PLACE DEDICATED ORDER (NO SERVER ALLOCATION) ===");

  try {
    // Get user ID through authentication
    $user_id = auth_user();
    error_log("User authenticated: $user_id");

    // Extract parameters with string escaping for direct SQL
    $os_id = isset($_GET['os_id']) ? intval($_GET['os_id']) : 1;
    $price = isset($_GET['price']) ? floatval($_GET['price']) : 549;
    $config_id = isset($_GET['config_id']) ? intval($_GET['config_id']) : 1;
    $location_id = isset($_GET['location_id']) ? intval($_GET['location_id']) : 1;
    $subnet_id = isset($_GET['subnet_id']) ? intval($_GET['subnet_id']) : 1;

    // Generate a random password - properly escaped for SQL
    $random_password = substr(md5(uniqid(rand(), true)), 0, 12);
    $escaped_password = $pdo->quote($random_password);

    // Transaction for order creation
    $pdo->beginTransaction();
    error_log("Started transaction for order creation");

    try {
      // Create order with PendingPayment status and NO assigned server
      $insert_order_sql = "
        INSERT INTO orders (
          label, owner_id, placed_by_id, order_type, order_date, expiration_date,
          initial_price, recurring_price, config_id, assigned_server_id, assigned_server_type,
          root_password, status, billing_cycle, subnet_config_id, os_id
        ) VALUES (
          'ZET-TEMP', $user_id, $user_id, 'dedicated', NOW(),
          DATE_ADD(NOW(), INTERVAL 1 MONTH), $price, $price, $config_id,
          0, 'dedicated', $escaped_password, 'PendingPayment', 1, $subnet_id, $os_id
        )
      ";

      error_log("Insert Order SQL: $insert_order_sql");
      $pdo->exec($insert_order_sql);
      $order_id = $pdo->lastInsertId();
      error_log("Order created with ID: $order_id");

      // Update the label
      $update_label_sql = "UPDATE orders SET label = 'ZET$order_id' WHERE id = $order_id";
      error_log("Update label SQL: $update_label_sql");
      $pdo->exec($update_label_sql);

      // Commit the order creation transaction
      $pdo->commit();
      error_log("Committed order creation transaction");

      // Response
      $reply = array(
        'success' => true,
        'orderid' => $order_id,
        'message' => 'Order created successfully',
        'price' => $price,
        'server_allocated' => false
      );

      error_log("Success response: " . json_encode($reply));
      die(json_encode($reply));
    } catch (Exception $e) {
      // Rollback if there's an active transaction
      if ($pdo->inTransaction()) {
        $pdo->rollBack();
        error_log("Rolled back order creation transaction due to error");
      }
      throw $e;
    }
  } catch (Exception $e) {
    error_log("Error in place dedicated order: " . $e->getMessage());

    $reply = array(
      'success' => false,
      'error' => $e->getMessage()
    );

    die(json_encode($reply));
  }
}

// PART 2: Modified process_paid_invoice function - ADD server allocation logic here
elseif($_GET['f'] == 'process_paid_invoice'){
  try {
    error_log("=== PROCESS PAID INVOICE WITH SERVER ALLOCATION CALLED ===");

    // Authenticate user
    $user_id = auth_user();
    error_log("User authenticated: user_id = $user_id");

    // Get request data
    $data = json_decode(file_get_contents('php://input'), true);
    error_log("Request data: " . json_encode($data));

    // Validate invoice_id parameter
    if (!isset($data['invoice_id'])) {
      throw new Exception("Missing invoice_id parameter");
    }

    $invoice_id = $data['invoice_id'];

    // Get the invoice
    $sth = $pdo->prepare("
      SELECT i.*, o.id as order_id, o.config_id, o.subnet_config_id, o.status as order_status
      FROM invoices i
      LEFT JOIN orders o ON i.order_id = o.id
      WHERE i.id = :invoice_id AND i.user_id = :user_id
    ");
    $sth->bindValue(':invoice_id', $invoice_id);
    $sth->bindValue(':user_id', $user_id);
    $sth->execute();

    if ($sth->rowCount() == 0) {
      throw new Exception("Invoice not found or doesn't belong to this user");
    }

    $invoice = $sth->fetch(PDO::FETCH_ASSOC);
    $order_id = $invoice['order_id'];

    // Check if invoice is already paid
    if ($invoice['paid'] == 1) {
      return json_encode([
        'success' => true,
        'message' => 'Invoice already marked as paid',
        'already_paid' => true
      ]);
    }

    // Begin transaction
    $pdo->beginTransaction();

    try {
      // Mark the invoice as paid
      $sth = $pdo->prepare("
        UPDATE invoices
        SET paid = 1,
            paid_date = NOW()
        WHERE id = :invoice_id
      ");
      $sth->bindValue(':invoice_id', $invoice_id);
      $sth->execute();

      // HANDLE ORDER & SERVER ALLOCATION - Only if this is an order-related invoice
      if ($order_id) {
        error_log("Processing order ID: $order_id");

        // Get order details
        $order_sth = $pdo->prepare("SELECT * FROM orders WHERE id = :order_id");
        $order_sth->bindValue(':order_id', $order_id);
        $order_sth->execute();
        $order = $order_sth->fetch(PDO::FETCH_ASSOC);

        if ($order) {
          $config_id = $order['config_id'];
          $os_id = $order['os_id'] ?: 1;  // Default to OS ID 1 if not set

          error_log("Order type: " . $order['order_type']);
          // Only proceed with server allocation for dedicated orders
          if ($order['order_type'] == 'dedicated') {
            error_log("Trying to allocate server for dedicated order");

            // Look for an available server
            $server_check_sql = "
              SELECT s.id
              FROM servers s
              INNER JOIN dedicated_configs dc ON s.config_id = dc.id
              WHERE s.status = 'Available'
              AND dc.id = $config_id
              LIMIT 1
            ";

            error_log("Server check SQL: $server_check_sql");
            $server_result = $pdo->query($server_check_sql);
            $server_allocated = false;
            $server_id = 0;

            if ($server_result && $server_result->rowCount() > 0) {
              $row = $server_result->fetch(PDO::FETCH_ASSOC);
              $server_id = $row['id'];
              $server_allocated = true;
              error_log("Found server ID: $server_id");

              // Update server status
              $update_server_sql = "
                UPDATE servers
                SET status = 'Allocated',
                    order_id = $order_id,
                    os_id = $os_id
                WHERE id = $server_id
              ";
              error_log("Update server SQL: $update_server_sql");
              $pdo->exec($update_server_sql);

              // Update order with assigned server and status=Installing
              $update_order_sql = "
                UPDATE orders
                SET status = 'Installing',
                    assigned_server_id = $server_id
                WHERE id = $order_id
              ";
              error_log("Update order SQL: $update_order_sql");
              $pdo->exec($update_order_sql);

              error_log("Server allocation successful");
            } else {
              error_log("No available server found - leaving in PendingPayment state");
            }
          }
        }
      }

      // Commit transaction
      $pdo->commit();

      // Send paid invoice email notification
      try {
        // Check if invoice_email_functions.php exists and include it
        if (file_exists('invoice_email_functions.php')) {
          require_once('invoice_email_functions.php');

          // Check if the sendInvoicePaidEmail function exists
          if (function_exists('sendInvoicePaidEmail')) {
            error_log("Sending paid invoice email notification for invoice #$invoice_id");
            $emailResult = sendInvoicePaidEmail($invoice_id);

            if ($emailResult) {
              error_log("Successfully sent paid invoice email notification for invoice #$invoice_id");
            } else {
              error_log("Failed to send paid invoice email notification for invoice #$invoice_id");
            }
          } else {
            error_log("sendInvoicePaidEmail function not found");
          }
        } else {
          error_log("invoice_email_functions.php file not found");
        }
      } catch (Exception $emailError) {
        error_log("Error sending paid invoice email notification: " . $emailError->getMessage());
        // Don't throw the exception - we don't want to fail the invoice payment if email fails
      }

      // Success response
      $response = [
        'success' => true,
        'message' => 'Invoice marked as paid' . ($server_allocated ? ' and server allocated' : ''),
        'invoice_type' => $invoice['type'],
        'server_allocated' => $server_allocated ?? false,
        'server_id' => $server_id ?? 0
      ];

      echo json_encode($response);

    } catch (Exception $e) {
      if ($pdo->inTransaction()) {
        $pdo->rollBack();
      }
      throw $e;
    }

  } catch (Exception $e) {
    error_log("Error in process_paid_invoice: " . $e->getMessage());
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }

  exit;
}


elseif($_GET['f'] == 'dedicatedorder'){
  try {
    // Start with detailed debug logging
    error_log("=== DEDICATED ORDER ENDPOINT CALLED ===");
    error_log("Request parameters: " . json_encode($_GET));

    // Authenticate the user
    $user_id = auth_user();
    error_log("User authenticated: $user_id");

    // Get parameters with defaults
    $cpuram_id = isset($_GET['model_id']) ? $_GET['model_id'] : null;
    $storage_id = isset($_GET['storage_id']) ? $_GET['storage_id'] : null;
    $bandwidth_id = isset($_GET['bandwidth_id']) ? $_GET['bandwidth_id'] : null;
    $country_id = isset($_GET['location_id']) ? $_GET['location_id'] : null;
    $subnet_id = isset($_GET['subnet_id']) ? $_GET['subnet_id'] : null;
    $os_id = isset($_GET['os_id']) ? $_GET['os_id'] : null;
    $servers_number = isset($_GET['servers_number']) ? $_GET['servers_number'] : null;

    // Prepare the response array - MUST be an array to match frontend expectations
    $response = array();
    $config = array();

    // Get default CPU/RAM option if not specified
    if($cpuram_id == null){
        error_log("No model_id specified, getting default");
        $sth = $pdo->prepare("SELECT * FROM dedicated_cpuram ORDER BY id ASC LIMIT 0,1");
        $sth->execute();
        if ($sth->rowCount() > 0) {
            $row = $sth->fetch();
            $cpuram_id = $row['id'];
            error_log("Using default model_id: $cpuram_id");
        } else {
            // No CPU/RAM models in database - use hardcoded default
            $cpuram_id = 1;
            error_log("No models in database, using default ID: $cpuram_id");
        }
    }

    // === MODEL OPTIONS ===
    // Get models with pricing - fallback to dummy data if DB query fails
    $models = array();
    try {
        $sth = $pdo->prepare("SELECT id, cpu, ram FROM dedicated_cpuram ORDER BY id ASC");
        $sth->execute();

        while($row = $sth->fetch(PDO::FETCH_ASSOC)){
            $models[] = array(
                "id" => $row['id'],
                "cpu" => $row['cpu'],
                "ram" => $row['ram'],
                "price" => "0", // Set default price to 0
                "checked" => ($cpuram_id == $row['id'])
            );
        }

        error_log("Found " . count($models) . " CPU/RAM models");
    } catch (Exception $e) {
        error_log("Error fetching models: " . $e->getMessage() . ". Using fallback data.");
    }

    // If no models found, provide fallback data
    if(empty($models)) {
        error_log("Using fallback model data");
        $models = array(
            array(
                "id" => "1",
                "cpu" => "2x Intel Xeon",
                "ram" => "64GB RAM",
                "price" => "0",
                "checked" => ($cpuram_id == "1" || $cpuram_id == null)
            ),
            array(
                "id" => "2",
                "cpu" => "2x AMD EPYC",
                "ram" => "128GB RAM",
                "price" => "250",
                "checked" => ($cpuram_id == "2")
            )
        );

        // Set cpuram_id if it's still null
        if ($cpuram_id == null) {
            $cpuram_id = "1";
        }
    }

    $config['models'] = $models;

    // === STORAGE OPTIONS ===
    // Get default storage option if not specified
    if($storage_id == null){
        error_log("No storage_id specified, getting default");
        try {
            $sth = $pdo->prepare("SELECT id FROM dedicated_storages ORDER BY id ASC LIMIT 0,1");
            $sth->execute();
            if ($sth->rowCount() > 0) {
                $row = $sth->fetch();
                $storage_id = $row['id'];
                error_log("Using default storage_id: $storage_id");
            } else {
                $storage_id = 1;
                error_log("No storages in database, using default ID: $storage_id");
            }
        } catch (Exception $e) {
            $storage_id = 1;
            error_log("Error getting default storage: " . $e->getMessage() . ". Using ID: $storage_id");
        }
    }

    // Get storage options - fallback to dummy data if DB query fails
    $storages = array();
    try {
        $sth = $pdo->prepare("SELECT id, name FROM dedicated_storages ORDER BY id ASC");
        $sth->execute();

        while($row = $sth->fetch(PDO::FETCH_ASSOC)){
            $storages[] = array(
                "id" => $row['id'],
                "name" => $row['name'],
                "price" => "0", // Set default price to 0
                "checked" => ($storage_id == $row['id'])
            );
        }

        error_log("Found " . count($storages) . " storage options");
    } catch (Exception $e) {
        error_log("Error fetching storages: " . $e->getMessage() . ". Using fallback data.");
    }

    // If no storage options found, provide fallback data
    if(empty($storages)) {
        error_log("Using fallback storage data");
        $storages = array(
            array(
                "id" => "1",
                "name" => "2x 500GB SSD",
                "price" => "0",
                "checked" => ($storage_id == "1" || $storage_id == null)
            ),
            array(
                "id" => "2",
                "name" => "2x 1TB NVMe",
                "price" => "250",
                "checked" => ($storage_id == "2")
            )
        );

        // Set storage_id if it's still null
        if ($storage_id == null) {
            $storage_id = "1";
        }
    }

    $config['storage'] = $storages;

    // === BANDWIDTH OPTIONS ===
    // Get default bandwidth option if not specified
    if($bandwidth_id == null){
        error_log("No bandwidth_id specified, getting default");
        try {
            $sth = $pdo->prepare("SELECT id FROM dedicated_bandwidth ORDER BY id ASC LIMIT 0,1");
            $sth->execute();
            if ($sth->rowCount() > 0) {
                $row = $sth->fetch();
                $bandwidth_id = $row['id'];
                error_log("Using default bandwidth_id: $bandwidth_id");
            } else {
                $bandwidth_id = 1;
                error_log("No bandwidth options in database, using default ID: $bandwidth_id");
            }
        } catch (Exception $e) {
            $bandwidth_id = 1;
            error_log("Error getting default bandwidth: " . $e->getMessage() . ". Using ID: $bandwidth_id");
        }
    }

    // Get bandwidth options - fallback to dummy data if DB query fails
    $bandwidths = array();
    try {
        $sth = $pdo->prepare("SELECT id, name FROM dedicated_bandwidth ORDER BY id ASC");
        $sth->execute();

        while($row = $sth->fetch(PDO::FETCH_ASSOC)){
            $bandwidths[] = array(
                "id" => $row['id'],
                "name" => $row['name'],
                "price" => "0", // Set default price to 0
                "checked" => ($bandwidth_id == $row['id'])
            );
        }

        error_log("Found " . count($bandwidths) . " bandwidth options");
    } catch (Exception $e) {
        error_log("Error fetching bandwidths: " . $e->getMessage() . ". Using fallback data.");
    }

    // If no bandwidth options found, provide fallback data
    if(empty($bandwidths)) {
        error_log("Using fallback bandwidth data");
        $bandwidths = array(
            array(
                "id" => "1",
                "name" => "1Gbps Unmetered",
                "price" => "0",
                "checked" => ($bandwidth_id == "1" || $bandwidth_id == null)
            ),
            array(
                "id" => "2",
                "name" => "10Gbps Unmetered",
                "price" => "250",
                "checked" => ($bandwidth_id == "2")
            )
        );

        // Set bandwidth_id if it's still null
        if ($bandwidth_id == null) {
            $bandwidth_id = "1";
        }
    }

    $config['bandwidth'] = $bandwidths;

    // === LOCATION OPTIONS ===
    // Get default location option if not specified
    if($country_id == null){
        error_log("No location_id specified, getting default");
        try {
            $sth = $pdo->prepare("SELECT id FROM dedicated_countries ORDER BY id ASC LIMIT 0,1");
            $sth->execute();
            if ($sth->rowCount() > 0) {
                $row = $sth->fetch();
                $country_id = $row['id'];
                error_log("Using default location_id: $country_id");
            } else {
                $country_id = 1;
                error_log("No locations in database, using default ID: $country_id");
            }
        } catch (Exception $e) {
            $country_id = 1;
            error_log("Error getting default location: " . $e->getMessage() . ". Using ID: $country_id");
        }
    }

    // Get location options - fallback to dummy data if DB query fails
    $locations = array();
    try {
        $sth = $pdo->prepare("SELECT id, long_name, short_name FROM dedicated_countries ORDER BY id ASC");
        $sth->execute();

        while($row = $sth->fetch(PDO::FETCH_ASSOC)){
            // Check inventory for this configuration and location
            try {
                $stockSth = $pdo->prepare("
                    SELECT COUNT(*) as available_count
                    FROM servers
                    WHERE status = 'Available'
                    AND config_id IN (
                        SELECT id FROM dedicated_configs
                        WHERE cpuram_id = :cpuram_id
                        AND storage_id = :storage_id
                        AND bandwidth_id = :bandwidth_id
                        AND country_id = :country_id
                    )
                ");
                $stockSth->bindValue(':cpuram_id', $cpuram_id);
                $stockSth->bindValue(':storage_id', $storage_id);
                $stockSth->bindValue(':bandwidth_id', $bandwidth_id);
                $stockSth->bindValue(':country_id', $row['id']);
                $stockSth->execute();

                $stockRow = $stockSth->fetch(PDO::FETCH_ASSOC);
                $availableCount = intval($stockRow['available_count']);

                // Set stock message based on inventory
                if ($availableCount > 0) {
                    $stock = "$availableCount in stock";
                    $stockcolor = "green";
                    $has_stock = true;
                } else {
                    $stock = "Available in 2 weeks";
                    $stockcolor = "orange";
                    $has_stock = false;
                }
            } catch (Exception $e) {
                error_log("Error checking inventory: " . $e->getMessage());
                // Default stock message if inventory check fails
                $stock = "5+ in stock";
                $stockcolor = "green";
                $has_stock = true;
            }

            $locations[] = array(
                "id" => $row['id'],
                "name" => $row['long_name'],
                "flag" => $row['short_name'],
                "stock" => $stock,
                "stockcolor" => $stockcolor,
                "has_stock" => $has_stock,
                "price" => "0", // Set default price to 0
                "checked" => ($country_id == $row['id'])
            );
        }

        error_log("Found " . count($locations) . " location options");
    } catch (Exception $e) {
        error_log("Error fetching locations: " . $e->getMessage() . ". Using fallback data.");
    }

    // If no location options found, provide fallback data
    if(empty($locations)) {
        error_log("Using fallback location data");
        $locations = array(
            array(
                "id" => "1",
                "name" => "Amsterdam",
                "flag" => "nl",
                "stock" => "5+ in stock",
                "stockcolor" => "green",
                "has_stock" => true,
                "price" => "0",
                "checked" => ($country_id == "1" || $country_id == null)
            ),
            array(
                "id" => "2",
                "name" => "Frankfurt",
                "flag" => "de",
                "stock" => "5+ in stock",
                "stockcolor" => "green",
                "has_stock" => true,
                "price" => "0",
                "checked" => ($country_id == "2")
            )
        );

        // Set country_id if it's still null
        if ($country_id == null) {
            $country_id = "1";
        }
    }

    $config['location'] = $locations;

    // === SUBNET OPTIONS ===
    // Get subnet options or use fallback
    try {
        $sth = $pdo->prepare("SELECT id, name, price FROM dedicated_subnets ORDER BY id ASC");
        $sth->execute();

        $subnets = array();
        while($row = $sth->fetch(PDO::FETCH_ASSOC)){
            $subnets[] = array(
                "id" => $row['id'],
                "name" => $row['name'],
                "price" => $row['price'],
                "checked" => ($subnet_id == $row['id'] || ($subnet_id == null && $row['id'] == 1))
            );
        }

        error_log("Found " . count($subnets) . " subnet options");

        if (!empty($subnets)) {
            $config['subnet'] = $subnets;
        }
    } catch (Exception $e) {
        error_log("Error fetching subnets: " . $e->getMessage() . ". Using fallback data.");
    }

    // If no subnet options found (or on error), provide fallback data
    if(!isset($config['subnet']) || empty($config['subnet'])) {
        error_log("Using fallback subnet data");
        $config['subnet'] = array(
            array(
                "id" => "1",
                "name" => "/30 (1 usable IP)",
                "price" => "0",
                "checked" => ($subnet_id == "1" || $subnet_id == null)
            ),
            array(
                "id" => "2",
                "name" => "/29 (5 usable IPs)",
                "price" => "250",
                "checked" => ($subnet_id == "2")
            ),
            array(
                "id" => "3",
                "name" => "/28 (13 usable IPs)",
                "price" => "250",
                "checked" => ($subnet_id == "3")
            )
        );
    }

    // === OS OPTIONS ===
    // Get OS options or use fallback
    try {
        $sth = $pdo->prepare("SELECT id, os_name, logo_url, price FROM dedicated_os ORDER BY id ASC");
        $sth->execute();

        $oses = array();
        while($row = $sth->fetch(PDO::FETCH_ASSOC)){
            $oses[] = array(
                "id" => $row['id'],
                "name" => $row['os_name'],
                "icon" => $row['logo_url'] ?: "ubuntu.png", // Default icon if not provided
                "price" => $row['price'] ?: "0",
                "checked" => ($os_id == $row['id'] || ($os_id == null && $row['id'] == 1))
            );
        }

        error_log("Found " . count($oses) . " OS options");

        if (!empty($oses)) {
            $config['os'] = $oses;
        }
    } catch (Exception $e) {
        error_log("Error fetching OS options: " . $e->getMessage() . ". Using fallback data.");
    }

    // If no OS options found (or on error), provide fallback data
    if(!isset($config['os']) || empty($config['os'])) {
        error_log("Using fallback OS data");
        $config['os'] = array(
            array(
                "id" => "1",
                "name" => "Ubuntu 20.04 LTS",
                "icon" => "ubuntu.png",
                "price" => "0",
                "checked" => ($os_id == "1" || $os_id == null)
            ),
            array(
                "id" => "2",
                "name" => "Ubuntu 22.04 LTS",
                "icon" => "ubuntu.png",
                "price" => "0",
                "checked" => ($os_id == "2")
            ),
            array(
                "id" => "3",
                "name" => "CentOS 8",
                "icon" => "centos.png",
                "price" => "0",
                "checked" => ($os_id == "3")
            )
        );
    }

    // === SERVERS NUMBER OPTIONS ===
    // Provide servers number options (this is typically hardcoded)
    $config['servers_number'] = array(
        array(
            "id" => "1",
            "name" => "1 (0% discount)",
            "checked" => ($servers_number == "" || $servers_number == "1" || $servers_number == null)
        ),
        array(
            "id" => "2",
            "name" => "2 (0% discount)",
            "checked" => ($servers_number == "2")
        ),
        array(
            "id" => "3",
            "name" => "3 (0% discount)",
            "checked" => ($servers_number == "3")
        ),
        array(
            "id" => "4",
            "name" => "4 (0% discount)",
            "checked" => ($servers_number == "4")
        ),
        array(
            "id" => "5",
            "name" => "5 (10% discount)",
            "checked" => ($servers_number == "5")
        ),
        array(
            "id" => "10",
            "name" => "10 (20% discount)",
            "checked" => ($servers_number == "10")
        )
    );

    // === FETCH PRICE ===
    // Try to get the price from the database using the selected options
    try {
        $priceSth = $pdo->prepare("
            SELECT price
            FROM dedicated_configs
            WHERE cpuram_id = :cpuram_id
            AND storage_id = :storage_id
            AND bandwidth_id = :bandwidth_id
            AND country_id = :country_id
            AND is_hidden = 0
            LIMIT 1
        ");

        $priceSth->bindValue(':cpuram_id', $cpuram_id);
        $priceSth->bindValue(':storage_id', $storage_id);
        $priceSth->bindValue(':bandwidth_id', $bandwidth_id);
        $priceSth->bindValue(':country_id', $country_id);
        $priceSth->execute();

        $priceRow = $priceSth->fetch(PDO::FETCH_ASSOC);

        if ($priceRow) {
            $config['price'] = floatval($priceRow['price']);
            error_log("Found price in database: " . $config['price']);
        } else {
            // If no price found in DB, try different combination or use fallback
            error_log("No price found for exact configuration, using default");
            $config['price'] = 549; // Default price
        }
    } catch (Exception $e) {
        error_log("Error fetching price: " . $e->getMessage() . ". Using default price.");
        $config['price'] = 549; // Default price on error
    }

    // Set delivery time based on location stock
    $selectedLocation = null;
    foreach ($config['location'] as $location) {
        if ($location['checked']) {
            $selectedLocation = $location;
            break;
        }
    }

    if ($selectedLocation) {
        if ($selectedLocation['has_stock']) {
            $config['delivery'] = "INSTANT (Server in stock)";
        } else {
            $config['delivery'] = ($selectedLocation['stockcolor'] === "green") ? "INSTANT" : "in 2 weeks";
        }
    } else {
        $config['delivery'] = "INSTANT"; // Default delivery time
    }

    // Add the config to the response array
    $response[] = $config;

    // Log the final response structure for debugging
    error_log("Response structure: " . json_encode(array_keys($config)));

    // Return JSON response - ensure proper JSON encoding
    header('Content-Type: application/json');
    $jsonResponse = json_encode($response);

    if ($jsonResponse === false) {
        throw new Exception("JSON encoding failed: " . json_last_error_msg());
    }

    echo $jsonResponse;
    exit;

  } catch (Exception $e) {
    // Log detailed error
    error_log("ERROR in dedicatedorder: " . $e->getMessage());
    error_log("Trace: " . $e->getTraceAsString());

    // Return error response in the format expected by the frontend
    header('Content-Type: application/json');
    echo json_encode([
        [
            "error" => $e->getMessage(),
            "models" => [["id" => "1", "cpu" => "Default Server", "ram" => "64GB RAM", "price" => "0", "checked" => true]],
            "storage" => [["id" => "1", "name" => "Default Storage", "price" => "0", "checked" => true]],
            "bandwidth" => [["id" => "1", "name" => "Default Bandwidth", "price" => "0", "checked" => true]],
            "location" => [["id" => "1", "name" => "Default Location", "flag" => "us", "stock" => "Available", "stockcolor" => "green", "price" => "0", "checked" => true]],
            "subnet" => [["id" => "1", "name" => "Default Subnet", "price" => "0", "checked" => true]],
            "os" => [["id" => "1", "name" => "Default OS", "icon" => "ubuntu.png", "price" => "0", "checked" => true]],
            "servers_number" => [["id" => "1", "name" => "1 (0% discount)", "checked" => true]],
            "price" => 549,
            "delivery" => "INSTANT"
        ]
    ]);
    exit;
  }
}


elseif($_GET['f'] == 'cloudorder'){
?>[
  {
    "models": [{
      "id": "1",
      "cpu": "2 Dedicated vCPU",
      "ram": "4 GB RAM",
      "disk": "40 GB NVMe",
      "price": "0",
      "checked": <?php if($_GET['model_id'] == "" OR $_GET['model_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "cpu": "4 Dedicated vCPU",
      "ram": "8 GB RAM",
      "disk": "80 GB NVMe",
      "price": "250",
      "checked": <?php if($_GET['model_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "cpu": "8 Dedicated vCPU",
      "ram": "16 GB RAM",
      "disk": "160 GB NVMe",
      "price": "250",
      "checked": <?php if($_GET['model_id'] == "3") echo "true"; else echo "false"; ?>
    }]
    ,
    "os": [{
      "id": "1",
      "name": "Ubuntu 20.04 LTS",
      "icon": "ubuntu.png",
      "price": "0",
      "checked": <?php if($_GET['os_id'] == "" OR $_GET['os_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "Ubuntu 22.04 LTS",
      "icon": "ubuntu.png",
      "price": "250",
      "checked": <?php if($_GET['os_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "CentOS 8",
      "icon": "centos.png",
      "price": "250",
      "checked": <?php if($_GET['os_id'] == "3") echo "true"; else echo "false"; ?>
    }],
    "location": [{
      "id": "1",
      "name": "Bucharest",
      "flag": "ro",
      "price": "0",
      "checked": <?php if($_GET['location_id'] == "" OR $_GET['location_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "Amsterdam",
      "flag": "nl",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "Frankfurt",
      "flag": "de",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "3") echo "true"; else echo "false"; ?>
    },
    {
      "id": "4",
      "name": "London",
      "flag": "gb",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "4") echo "true"; else echo "false"; ?>
    },
    {
      "id": "5",
      "name": "Marseille",
      "flag": "fr",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "5") echo "true"; else echo "false"; ?>
    },
    {
      "id": "6",
      "name": "Vienna",
      "flag": "at",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "6") echo "true"; else echo "false"; ?>
    },
    {
      "id": "7",
      "name": "Madrid",
      "flag": "es",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "7") echo "true"; else echo "false"; ?>
    }],
    "ip": [{
      "id": "1",
      "name": "1 x IPv4",
      "price": "0",
      "checked": <?php if($_GET['ip_id'] == "" OR $_GET['ip_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "2 x IPv4",
      "price": "2",
      "checked": <?php if($_GET['ip_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "3 x IPv4",
      "price": "4",
      "checked": <?php if($_GET['ip_id'] == "3") echo "true"; else echo "false"; ?>
    },
    {
      "id": "4",
      "name": "4 x IPv4",
      "price": "6",
      "checked": <?php if($_GET['ip_id'] == "4") echo "true"; else echo "false"; ?>
    },
    {
      "id": "5",
      "name": "5 x IPv4",
      "price": "8",
      "checked": <?php if($_GET['ip_id'] == "5") echo "true"; else echo "false"; ?>
    }],
    "price": <?php if($_GET['model_id'] == 2) echo "79"; else echo "49"; ?>,
    "delivery": "INSTANT"

  }]
<?php
}elseif($_GET['f'] == 'colocationorder'){
?>[
  {
    "rack": [{
      "id": "1",
      "size": "1 Rack Unit",
      "price": "0",
      "checked": <?php if($_GET['rack_id'] == "" OR $_GET['rack_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "size": "2 Rack Units",
      "price": "250",
      "checked": <?php if($_GET['rack_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "size": "10 Rack Units",
      "ram": "256GB DDR4 RAM",
      "price": "250",
      "checked": <?php if($_GET['rack_id'] == "3") echo "true"; else echo "false"; ?>
    },
    {
      "id": "4",
      "size": "20 Rack Units",
      "ram": "256GB DDR4 RAM",
      "price": "250",
      "checked": <?php if($_GET['rack_id'] == "4") echo "true"; else echo "false"; ?>
    },
    {
      "id": "5",
      "size": "Fuck Rack (42U)",
      "ram": "256GB DDR4 RAM",
      "price": "250",
      "checked": <?php if($_GET['rack_id'] == "5") echo "true"; else echo "false"; ?>
    }]
    ,
    <?php if($_GET['rack_id'] == '1' OR $_GET['rack_id'] == ''){ ?>

    "power": [{
      "id": "1",
      "name": "0.25kW (Redundand)",
      "price": "0",
      "checked": <?php if($_GET['power_id'] == "" OR $_GET['power_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "0.5kW (Redundand)",
      "price": "250",
      "checked": <?php if($_GET['power_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "1kW (Redundand)",
      "price": "250",
      "checked": <?php if($_GET['power_id'] == "3") echo "true"; else echo "false"; ?>
    }]<?php }else{ ?>"power": [{
      "id": "1",
      "name": "2kW (Redundand)",
      "price": "0",
      "checked": <?php if($_GET['power_id'] == "" OR $_GET['power_id'] == "1") echo "true"; else echo "false"; ?>
    }]<?php } ?>,
    "bandwidth": [{
      "id": "1",
      "name": "10Gbps Unmetered",
      "price": "0",
      "checked": <?php if($_GET['bandwidth_id'] == "" OR $_GET['bandwidth_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "25Gbps Unmetered",
      "price": "250",
      "checked": <?php if($_GET['bandwidth_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "50Gbps Unmetered",
      "price": "250",
      "checked": <?php if($_GET['bandwidth_id'] == "3") echo "true"; else echo "false"; ?>
    }],
    "location": [{
      "id": "1",
      "name": "Bucharest",
      "stock": "in 24 hours",
      "stockcolor": "green",
      "flag": "ro",
      "price": "0",
      "checked": <?php if($_GET['location_id'] == "" OR $_GET['location_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "Amsterdam",
      "stock": "in 24 hours",
      "stockcolor": "green",
      "flag": "nl",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "Frankfurt",
      "stock": "in 24 hours",
      "stockcolor": "green",
      "flag": "de",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "3") echo "true"; else echo "false"; ?>
    },
    {
      "id": "4",
      "name": "London",
      "stock": "in 4 weeks",
      "stockcolor": "red",
      "flag": "gb",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "4") echo "true"; else echo "false"; ?>
    },
    {
      "id": "5",
      "name": "Marseille",
      "stock": "in 4 weeks",
      "stockcolor": "red",
      "flag": "fr",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "5") echo "true"; else echo "false"; ?>
    },
    {
      "id": "6",
      "name": "Vienna",
      "stock": "in 4 weeks",
      "stockcolor": "red",
      "flag": "at",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "6") echo "true"; else echo "false"; ?>
    },
    {
      "id": "7",
      "name": "Madrid",
      "stock": "in 4 weeks",
      "stockcolor": "red",
      "flag": "es",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "7") echo "true"; else echo "false"; ?>
    }],
    "subnet": [{
      "id": "1",
      "name": "/30 (1 usable IP)",
      "price": "0",
      "checked": <?php if($_GET['subnet_id'] == "" OR $_GET['subnet_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "/29 (5 usable IPs)",
      "price": "250",
      "checked": <?php if($_GET['subnet_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "/28 (13 usable IPs)",
      "price": "250",
      "checked": <?php if($_GET['subnet_id'] == "3") echo "true"; else echo "false"; ?>
    }],
    "contract": [{
      "id": "1",
      "name": "12 Months",
      "price": "0",
      "checked": <?php if($_GET['contract_id'] == "" OR $_GET['contract_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "24 Months",
      "price": "250",
      "checked": <?php if($_GET['contract_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "36 Months",
      "price": "250",
      "checked": <?php if($_GET['contract_id'] == "3") echo "true"; else echo "false"; ?>
    }],
    "price": <?php if($_GET['rack_id'] == 2) echo "499"; else echo "1000"; ?>,
    "delivery": <?php if($_GET['rack_id'] == 2) echo "\"in 24 hours\""; else echo "\"in 4 weeks\""; ?>

  }]
<?php
}elseif($_GET['f'] == 'colocationassetorder'){
?>[
  {
    "cat": [{
      "id": "1",
      "name": "Rackable Server",
      "price": "0",
      "checked": <?php if($_GET['cat_id'] == "" OR $_GET['cat_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "Blade Server",
      "price": "250",
      "checked": <?php if($_GET['cat_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "Switch",
      "ram": "256GB DDR4 RAM",
      "price": "250",
      "checked": <?php if($_GET['cat_id'] == "3") echo "true"; else echo "false"; ?>
    }]
    ,
    <?php if($_GET['cat_id'] == '1' OR $_GET['cat_id'] == ''){ ?>

    "asset": [{
      "id": "1",
      "name": "Dual E5-2680v4",
      "price": "0",
      "checked": <?php if($_GET['asset_id'] == "" OR $_GET['asset_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "Dual E5-2699v4",
      "price": "250",
      "checked": <?php if($_GET['asset_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "Dual Gold 6148",
      "price": "250",
      "checked": <?php if($_GET['asset_id'] == "3") echo "true"; else echo "false"; ?>
    }]<?php }else{ ?>"asset": [{
      "id": "2",
      "name": "4 x Dual E5-2699v4",
      "price": "250",
      "checked": <?php if($_GET['asset_id'] == "1") echo "true"; else echo "false"; ?>
    }]<?php } ?>,
    "colo": [{
      "id": "1",
      "name": "Rack #105 AM6",
      "price": "0",
      "checked": <?php if($_GET['colo_id'] == "" OR $_GET['colo_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "Rack #55 BC1",
      "price": "250",
      "checked": <?php if($_GET['colo_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "Rack #142 FR5",
      "price": "250",
      "checked": <?php if($_GET['colo_id'] == "3") echo "true"; else echo "false"; ?>
    }],
    "units": [{
      "id": "1",
      "name": "RU 1-2",
      "stock": "in 24 hours",
      "stockcolor": "green",
      "flag": "ro",
      "price": "0",
      "checked": <?php if($_GET['units_id'] == "" OR $_GET['units_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "RU 3-4",
      "stock": "in 24 hours",
      "stockcolor": "green",
      "flag": "nl",
      "price": "250",
      "checked": <?php if($_GET['units_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "RU 5-6",
      "stock": "in 24 hours",
      "stockcolor": "green",
      "flag": "de",
      "price": "250",
      "checked": <?php if($_GET['units_id'] == "3") echo "true"; else echo "false"; ?>
    },
    {
      "id": "4",
      "name": "RU 7-8",
      "stock": "in 24 hours",
      "stockcolor": "green",
      "flag": "de",
      "price": "250",
      "checked": <?php if($_GET['units_id'] == "4") echo "true"; else echo "false"; ?>
    },
    {
      "id": "5",
      "name": "RU 9-10",
      "stock": "in 4 weeks",
      "stockcolor": "red",
      "flag": "es",
      "price": "250",
      "checked": <?php if($_GET['units_id'] == "5") echo "true"; else echo "false"; ?>
    }],
    "renting": [{
      "id": "1",
      "name": "Regular Rental",
      "price": "0",
      "checked": <?php if($_GET['renting_id'] == "" OR $_GET['renting_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "Lease to Own",
      "price": "250",
      "checked": <?php if($_GET['renting_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "Buy Now",
      "price": "250",
      "checked": <?php if($_GET['renting_id'] == "3") echo "true"; else echo "false"; ?>
    }],
    "contract": [{
      "id": "1",
      "name": "12 Months",
      "price": "0",
      "checked": <?php if($_GET['contract_id'] == "" OR $_GET['contract_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "24 Months",
      "price": "250",
      "checked": <?php if($_GET['contract_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "36 Months",
      "price": "250",
      "checked": <?php if($_GET['contract_id'] == "3") echo "true"; else echo "false"; ?>
    }],
    "price": <?php if($_GET['rack_id'] == 2) echo "499"; else echo "1000"; ?>,
    "delivery": <?php if($_GET['rack_id'] == 2) echo "\"in 24 hours\""; else echo "\"in 4 weeks\""; ?>

  }]
<?php
}elseif($_GET['f'] == 'iptransitorder'){
?>[
  {
    "porttype": [{
      "id": "1",
      "size": "Flat Port",
      "price": "0",
      "checked": <?php if($_GET['porttype_id'] == "" OR $_GET['porttype_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "size": "Burstable Port",
      "price": "250",
      "checked": <?php if($_GET['porttype_id'] == "2") echo "true"; else echo "false"; ?>
    }]
    ,
    <?php if($_GET['porttype_id'] == '1' OR $_GET['porttype_id'] == ''){ ?>

    "portsize": [{
      "id": "1",
      "name": "10Gbps SFP+",
      "price": "0",
      "checked": <?php if($_GET['portsize_id'] == "" OR $_GET['portsize_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "100Gbps QSFP28 LR4",
      "price": "250",
      "checked": <?php if($_GET['portsize_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "2 x 100Gbps QSFP28 LR4",
      "price": "250",
      "checked": <?php if($_GET['portsize_id'] == "3") echo "true"; else echo "false"; ?>
    }]<?php }else{ ?>"portsize": [{
      "id": "1",
      "name": "100Gbps QSFP28 LR4",
      "price": "0",
      "checked": <?php if($_GET['portsize_id'] == "" OR $_GET['portsize_id'] == "1") echo "true"; else echo "false"; ?>
    }]<?php } ?>,
    "bandwidth": [{
      "id": "1",
      "name": "10Gbps",
      "price": "0",
      "checked": <?php if($_GET['bandwidth_id'] == "" OR $_GET['bandwidth_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "25Gbps",
      "price": "250",
      "checked": <?php if($_GET['bandwidth_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "50Gbps",
      "price": "250",
      "checked": <?php if($_GET['bandwidth_id'] == "3") echo "true"; else echo "false"; ?>
    }],
    "location": [{
      "id": "1",
      "name": "Bucharest",
      "stock": "in 24 hours",
      "stockcolor": "green",
      "flag": "ro",
      "price": "0",
      "checked": <?php if($_GET['location_id'] == "" OR $_GET['location_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "Amsterdam",
      "stock": "in 24 hours",
      "stockcolor": "green",
      "flag": "nl",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "Frankfurt",
      "stock": "in 24 hours",
      "stockcolor": "green",
      "flag": "de",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "3") echo "true"; else echo "false"; ?>
    },
    {
      "id": "4",
      "name": "London",
      "stock": "in 4 weeks",
      "stockcolor": "red",
      "flag": "gb",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "4") echo "true"; else echo "false"; ?>
    },
    {
      "id": "5",
      "name": "Marseille",
      "stock": "in 4 weeks",
      "stockcolor": "red",
      "flag": "fr",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "5") echo "true"; else echo "false"; ?>
    },
    {
      "id": "6",
      "name": "Vienna",
      "stock": "in 4 weeks",
      "stockcolor": "red",
      "flag": "at",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "6") echo "true"; else echo "false"; ?>
    },
    {
      "id": "7",
      "name": "Madrid",
      "stock": "in 4 weeks",
      "stockcolor": "red",
      "flag": "es",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "7") echo "true"; else echo "false"; ?>
    }],
    "subnet": [{
      "id": "1",
      "name": "No IPs (BGP Only)",
      "price": "0",
      "checked": <?php if($_GET['subnet_id'] == "" OR $_GET['subnet_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "/30 (1 usable IP)",
      "price": "0",
      "checked": <?php if($_GET['subnet_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "/29 (5 usable IPs)",
      "price": "250",
      "checked": <?php if($_GET['subnet_id'] == "3") echo "true"; else echo "false"; ?>
    },
    {
      "id": "4",
      "name": "/28 (13 usable IPs)",
      "price": "250",
      "checked": <?php if($_GET['subnet_id'] == "4") echo "true"; else echo "false"; ?>
    }],
    "contract": [{
      "id": "1",
      "name": "12 Months",
      "price": "0",
      "checked": <?php if($_GET['contract_id'] == "" OR $_GET['contract_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "24 Months",
      "price": "250",
      "checked": <?php if($_GET['contract_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "36 Months",
      "price": "250",
      "checked": <?php if($_GET['contract_id'] == "3") echo "true"; else echo "false"; ?>
    }],
    "price": <?php if($_GET['porttype_id'] == 2) echo "499"; else echo "1000"; ?>,
    "delivery": <?php if($_GET['porttype_id'] == 2) echo "\"in 24 hours\""; else echo "\"in 4 weeks\""; ?>

  }]
<?php
}




elseif($_GET['f'] == 'user_details'){
  $user_id = auth_user();
  $sth = $pdo->prepare("SELECT * FROM `users` WHERE `id` = :user_id");
  $sth->bindValue(':user_id', $user_id);
  $sth->execute();

  if($sth->rowCount() == 1){
      $row = $sth->fetch(PDO::FETCH_ASSOC);

      // Remove sensitive data
      unset($row['password']);
      unset($row['last_session']);

      die(json_encode($row));
  } else {
      $reply['error'] = 4; // User not found
      die(json_encode($reply));
  }

} elseif($_GET['f'] == 'update_user'){
  $user_id = auth_user();
  $data = json_decode(file_get_contents('php://input'), true);

  // Validate required fields
  if(!isset($data['first_name']) || !isset($data['last_name']) || !isset($data['email']) ||
     !isset($data['address']) || !isset($data['city']) || !isset($data['country'])){
      $reply['error'] = 3; // Missing required fields
      die(json_encode($reply));
  }

  // Update user details
  $sth = $pdo->prepare("UPDATE `users` SET
                       `first_name` = :first_name,
                       `last_name` = :last_name,
                       `company_name` = :company_name,
                       `vat_id` = :vat_id,
                       `email` = :email,
                       `address` = :address,
                       `city` = :city,
                       `country` = :country
                       WHERE `id` = :user_id");

  $sth->bindValue(':first_name', $data['first_name']);
  $sth->bindValue(':last_name', $data['last_name']);
  $sth->bindValue(':company_name', $data['company_name']);
  $sth->bindValue(':vat_id', $data['vat_id']);
  $sth->bindValue(':email', $data['email']);
  $sth->bindValue(':address', $data['address']);
  $sth->bindValue(':city', $data['city']);
  $sth->bindValue(':country', $data['country']);
  $sth->bindValue(':user_id', $user_id);

  if($sth->execute()){
      $reply['success'] = true;
      die(json_encode($reply));
  } else {
      $reply['error'] = 6; // Database update error
      die(json_encode($reply));
  }

} elseif($_GET['f'] == 'change_password'){
  $user_id = auth_user();
  $data = json_decode(file_get_contents('php://input'), true);

  if(!isset($data['current_password']) || !isset($data['new_password'])){
      $reply['error'] = 3; // Missing required fields
      die(json_encode($reply));
  }

  // Verify current password
  $sth = $pdo->prepare("SELECT * FROM `users` WHERE `id` = :user_id AND `password` = :current_password");
  $sth->bindValue(':user_id', $user_id);
  $sth->bindValue(':current_password', md5($data['current_password']));
  $sth->execute();

  if($sth->rowCount() != 1){
      $reply['error'] = 1; // Current password is incorrect
      die(json_encode($reply));
  }

  // Update password
  $sth = $pdo->prepare("UPDATE `users` SET `password` = :new_password WHERE `id` = :user_id");
  $sth->bindValue(':new_password', md5($data['new_password']));
  $sth->bindValue(':user_id', $user_id);

  if($sth->execute()){
      $reply['success'] = true;
      die(json_encode($reply));
  } else {
      $reply['error'] = 6; // Database update error
      die(json_encode($reply));
  }

} elseif($_GET['f'] == 'get_api_keys'){
  $user_id = auth_user();

  // Check if API keys table exists, create it if it doesn't
  $sth = $pdo->prepare("SHOW TABLES LIKE 'api_keys'");
  $sth->execute();

  if($sth->rowCount() == 0){
      // Create the api_keys table
      $sth = $pdo->prepare("CREATE TABLE `api_keys` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `user_id` int(11) NOT NULL,
          `name` varchar(128) NOT NULL,
          `api_key` varchar(64) NOT NULL,
          `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          `last_used` timestamp NULL DEFAULT NULL,
          PRIMARY KEY (`id`)
      ) ENGINE=InnoDB DEFAULT CHARSET=latin1;");
      $sth->execute();
  }

  // Get user's API keys
  $sth = $pdo->prepare("SELECT * FROM `api_keys` WHERE `user_id` = :user_id");
  $sth->bindValue(':user_id', $user_id);
  $sth->execute();

  $api_keys = array();
  while($row = $sth->fetch(PDO::FETCH_ASSOC)){
      $key = array(
          'id' => $row['id'],
          'name' => $row['name'],
          'key' => $row['api_key'],
          'created' => $row['created'],
          'lastUsed' => $row['last_used'] ? $row['last_used'] : 'Never'
      );
      $api_keys[] = $key;
  }

  die(json_encode($api_keys));

} elseif($_GET['f'] == 'create_api_key'){
  $user_id = auth_user();
  $data = json_decode(file_get_contents('php://input'), true);

  if(!isset($data['key_name']) || trim($data['key_name']) == ''){
      $reply['error'] = 3; // Missing required fields
      die(json_encode($reply));
  }

  // Check if API keys table exists, create it if it doesn't
  $sth = $pdo->prepare("SHOW TABLES LIKE 'api_keys'");
  $sth->execute();

  if($sth->rowCount() == 0){
      // Create the api_keys table
      $sth = $pdo->prepare("CREATE TABLE `api_keys` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `user_id` int(11) NOT NULL,
          `name` varchar(128) NOT NULL,
          `api_key` varchar(64) NOT NULL,
          `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          `last_used` timestamp NULL DEFAULT NULL,
          PRIMARY KEY (`id`)
      ) ENGINE=InnoDB DEFAULT CHARSET=latin1;");
      $sth->execute();
  }

  // Generate new API key and secret
  $api_key = generateRandom(32);
  $secret = generateRandom(12);

  // Insert new API key
  $sth = $pdo->prepare("INSERT INTO `api_keys` (`user_id`, `name`, `api_key`) VALUES (:user_id, :name, :api_key)");
  $sth->bindValue(':user_id', $user_id);
  $sth->bindValue(':name', $data['key_name']);
  $sth->bindValue(':api_key', $api_key);

  if($sth->execute()){
      $reply['id'] = $pdo->lastInsertId();
      $reply['api_key'] = $api_key;
      $reply['secret'] = $secret;
      $reply['success'] = true;
      die(json_encode($reply));
  } else {
      $reply['error'] = 6; // Database insert error
      die(json_encode($reply));
  }

} elseif($_GET['f'] == 'regenerate_api_key'){
  $user_id = auth_user();
  $data = json_decode(file_get_contents('php://input'), true);

  if(!isset($data['key_id'])){
      $reply['error'] = 3; // Missing required fields
      die(json_encode($reply));
  }

  // Verify the API key belongs to this user
  $sth = $pdo->prepare("SELECT * FROM `api_keys` WHERE `id` = :key_id AND `user_id` = :user_id");
  $sth->bindValue(':key_id', $data['key_id']);
  $sth->bindValue(':user_id', $user_id);
  $sth->execute();

  if($sth->rowCount() != 1){
      $reply['error'] = 2; // Key not found or doesn't belong to user
      die(json_encode($reply));
  }

  // Generate new API key and secret
  $api_key = generateRandom(32);
  $secret = generateRandom(12);

  // Update API key
  $sth = $pdo->prepare("UPDATE `api_keys` SET `api_key` = :api_key, `created` = NOW() WHERE `id` = :key_id");
  $sth->bindValue(':api_key', $api_key);
  $sth->bindValue(':key_id', $data['key_id']);

  if($sth->execute()){
      $reply['api_key'] = $api_key;
      $reply['secret'] = $secret;
      $reply['success'] = true;
      die(json_encode($reply));
  } else {
      $reply['error'] = 6; // Database update error
      die(json_encode($reply));
  }

} elseif($_GET['f'] == 'delete_api_key'){
  $user_id = auth_user();
  $data = json_decode(file_get_contents('php://input'), true);

  if(!isset($data['key_id'])){
      $reply['error'] = 3; // Missing required fields
      die(json_encode($reply));
  }

  // Verify the API key belongs to this user
  $sth = $pdo->prepare("SELECT * FROM `api_keys` WHERE `id` = :key_id AND `user_id` = :user_id");
  $sth->bindValue(':key_id', $data['key_id']);
  $sth->bindValue(':user_id', $user_id);
  $sth->execute();

  if($sth->rowCount() != 1){
      $reply['error'] = 2; // Key not found or doesn't belong to user
      die(json_encode($reply));
  }

  // Delete API key
  $sth = $pdo->prepare("DELETE FROM `api_keys` WHERE `id` = :key_id");
  $sth->bindValue(':key_id', $data['key_id']);

  if($sth->execute()){
      $reply['success'] = true;
      die(json_encode($reply));
  } else {
      $reply['error'] = 6; // Database delete error
      die(json_encode($reply));
  }

} elseif($_GET['f'] == 'get_access_users'){
  $user_id = auth_user();

  // Check if access_users table exists, create it if it doesn't
  $sth = $pdo->prepare("SHOW TABLES LIKE 'access_users'");
  $sth->execute();

  if($sth->rowCount() == 0){
      // Create the access_users table
      $sth = $pdo->prepare("CREATE TABLE `access_users` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `owner_id` int(11) NOT NULL,
          `email` varchar(128) NOT NULL,
          `perm_services` tinyint(1) NOT NULL DEFAULT '0',
          `perm_invoices` tinyint(1) NOT NULL DEFAULT '0',
          `perm_billing` tinyint(1) NOT NULL DEFAULT '0',
          `perm_tickets` tinyint(1) NOT NULL DEFAULT '0',
          `perm_api` tinyint(1) NOT NULL DEFAULT '0',
          `perm_full_access` tinyint(1) NOT NULL DEFAULT '0',
          `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`)
      ) ENGINE=InnoDB DEFAULT CHARSET=latin1;");
      $sth->execute();
  }

  // Get user's access users
  $sth = $pdo->prepare("SELECT * FROM `access_users` WHERE `owner_id` = :user_id");
  $sth->bindValue(':user_id', $user_id);
  $sth->execute();

  $access_users = array();
  while($row = $sth->fetch(PDO::FETCH_ASSOC)){
      $access_users[] = $row;
  }

  die(json_encode($access_users));

} elseif($_GET['f'] == 'add_access_user'){
$user_id = auth_user();
$data = json_decode(file_get_contents('php://input'), true);

if(!isset($data['email']) || trim($data['email']) == ''){
    $reply['error'] = 3; // Missing required fields
    die(json_encode($reply));
}

// Check if access_users table exists, create it if it doesn't
$sth = $pdo->prepare("SHOW TABLES LIKE 'access_users'");
$sth->execute();

if($sth->rowCount() == 0){
    // Create the access_users table
    $sth = $pdo->prepare("CREATE TABLE `access_users` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `owner_id` int(11) NOT NULL,
        `email` varchar(128) NOT NULL,
        `perm_services` tinyint(1) NOT NULL DEFAULT '0',
        `perm_invoices` tinyint(1) NOT NULL DEFAULT '0',
        `perm_billing` tinyint(1) NOT NULL DEFAULT '0',
        `perm_tickets` tinyint(1) NOT NULL DEFAULT '0',
        `perm_api` tinyint(1) NOT NULL DEFAULT '0',
        `perm_full_access` tinyint(1) NOT NULL DEFAULT '0',
        `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=latin1;");
    $sth->execute();
}

// Check if the email already exists for this user
$sth = $pdo->prepare("SELECT * FROM `access_users` WHERE `owner_id` = :user_id AND `email` = :email");
$sth->bindValue(':user_id', $user_id);
$sth->bindValue(':email', $data['email']);
$sth->execute();

if($sth->rowCount() > 0){
    $reply['error'] = 7; // Email already exists
    die(json_encode($reply));
}

// Convert boolean permissions to integer values (0 or 1)
$perm_services = isset($data['permissions']['services']) && $data['permissions']['services'] ? 1 : 0;
$perm_invoices = isset($data['permissions']['invoices']) && $data['permissions']['invoices'] ? 1 : 0;
$perm_billing = isset($data['permissions']['billing']) && $data['permissions']['billing'] ? 1 : 0;
$perm_tickets = isset($data['permissions']['tickets']) && $data['permissions']['tickets'] ? 1 : 0;
$perm_api = isset($data['permissions']['api']) && $data['permissions']['api'] ? 1 : 0;
$perm_full_access = isset($data['permissions']['fullAccess']) && $data['permissions']['fullAccess'] ? 1 : 0;

// Debug output
error_log("Adding access user with permissions: " .
          "services=$perm_services, invoices=$perm_invoices, billing=$perm_billing, " .
          "tickets=$perm_tickets, api=$perm_api, full_access=$perm_full_access");

// Insert new access user
$sth = $pdo->prepare("INSERT INTO `access_users`
                     (`owner_id`, `email`, `perm_services`, `perm_invoices`, `perm_billing`,
                      `perm_tickets`, `perm_api`, `perm_full_access`)
                     VALUES
                     (:user_id, :email, :perm_services, :perm_invoices, :perm_billing,
                      :perm_tickets, :perm_api, :perm_full_access)");

$sth->bindValue(':user_id', $user_id);
$sth->bindValue(':email', $data['email']);
$sth->bindValue(':perm_services', $perm_services, PDO::PARAM_INT);
$sth->bindValue(':perm_invoices', $perm_invoices, PDO::PARAM_INT);
$sth->bindValue(':perm_billing', $perm_billing, PDO::PARAM_INT);
$sth->bindValue(':perm_tickets', $perm_tickets, PDO::PARAM_INT);
$sth->bindValue(':perm_api', $perm_api, PDO::PARAM_INT);
$sth->bindValue(':perm_full_access', $perm_full_access, PDO::PARAM_INT);

if($sth->execute()){
    $reply['user_id'] = $pdo->lastInsertId();
    $reply['success'] = true;
    die(json_encode($reply));
} else {
    $reply['error'] = 6; // Database insert error
    error_log("Error adding access user: " . $sth->errorInfo()[2]);
    die(json_encode($reply));
}

} elseif($_GET['f'] == 'update_user_permission'){
$user_id = auth_user();
$data = json_decode(file_get_contents('php://input'), true);

if(!isset($data['user_id']) || !isset($data['permission'])){
    $reply['error'] = 3; // Missing required fields
    die(json_encode($reply));
}

// Verify the access user belongs to this user
$sth = $pdo->prepare("SELECT * FROM `access_users` WHERE `id` = :access_user_id AND `owner_id` = :user_id");
$sth->bindValue(':access_user_id', $data['user_id']);
$sth->bindValue(':user_id', $user_id);
$sth->execute();

if($sth->rowCount() != 1){
    $reply['error'] = 2; // Access user not found or doesn't belong to user
    die(json_encode($reply));
}

// Get current user record to read existing permissions
$user_record = $sth->fetch(PDO::FETCH_ASSOC);

// Map permission to database column
$permission_map = array(
    'services' => 'perm_services',
    'invoices' => 'perm_invoices',
    'billing' => 'perm_billing',
    'tickets' => 'perm_tickets',
    'api' => 'perm_api',
    'fullAccess' => 'perm_full_access'
);

if(!isset($permission_map[$data['permission']])){
    $reply['error'] = 8; // Invalid permission
    die(json_encode($reply));
}

$column = $permission_map[$data['permission']];

// Determine new value - toggle the current value
// If value is explicitly provided, use it; otherwise toggle
if(isset($data['value'])){
    // Convert any value to integer 0 or 1
    $new_value = $data['value'] ? 1 : 0;
} else {
    // Toggle current value
    $new_value = $user_record[$column] ? 0 : 1;
}

// Debug output
error_log("Updating permission $column to $new_value for user " . $data['user_id']);

// Update the permission
$sth = $pdo->prepare("UPDATE `access_users` SET `$column` = :value WHERE `id` = :access_user_id");
$sth->bindValue(':value', $new_value, PDO::PARAM_INT);
$sth->bindValue(':access_user_id', $data['user_id']);

if($sth->execute()){
    // If fullAccess is set to 1, set all other permissions to 1
    if($column == 'perm_full_access' && $new_value == 1){
        $sth = $pdo->prepare("UPDATE `access_users`
                             SET `perm_services` = 1,
                                 `perm_invoices` = 1,
                                 `perm_billing` = 1,
                                 `perm_tickets` = 1,
                                 `perm_api` = 1
                             WHERE `id` = :access_user_id");
        $sth->bindValue(':access_user_id', $data['user_id']);
        $sth->execute();
    }

    // If any permission is set to 0 and fullAccess is 1, set fullAccess to 0
    if($new_value == 0 && $column != 'perm_full_access' && $user_record['perm_full_access'] == 1){
        $sth = $pdo->prepare("UPDATE `access_users` SET `perm_full_access` = 0 WHERE `id` = :access_user_id");
        $sth->bindValue(':access_user_id', $data['user_id']);
        $sth->execute();
    }

    // If all specific permissions are set to 1, set fullAccess to 1
    if($column != 'perm_full_access' && $new_value == 1){
        // Get updated record to check all permissions
        $sth = $pdo->prepare("SELECT * FROM `access_users` WHERE `id` = :access_user_id");
        $sth->bindValue(':access_user_id', $data['user_id']);
        $sth->execute();
        $updated_record = $sth->fetch(PDO::FETCH_ASSOC);

        // Check if all permissions are now 1
        if($updated_record['perm_services'] == 1 &&
           $updated_record['perm_invoices'] == 1 &&
           $updated_record['perm_billing'] == 1 &&
           $updated_record['perm_tickets'] == 1 &&
           $updated_record['perm_api'] == 1 &&
           $updated_record['perm_full_access'] == 0){

            // Set full_access to 1
            $sth = $pdo->prepare("UPDATE `access_users` SET `perm_full_access` = 1 WHERE `id` = :access_user_id");
            $sth->bindValue(':access_user_id', $data['user_id']);
            $sth->execute();
        }
    }

    $reply['success'] = true;
    $reply['new_value'] = $new_value;
    die(json_encode($reply));
} else {
    $reply['error'] = 6; // Database update error
    error_log("Error updating permission: " . $sth->errorInfo()[2]);
    die(json_encode($reply));
}
} elseif($_GET['f'] == 'remove_access_user'){
  $user_id = auth_user();
  $data = json_decode(file_get_contents('php://input'), true);

  if(!isset($data['user_id'])){
      $reply['error'] = 3; // Missing required fields
      die(json_encode($reply));
  }

  // Verify the access user belongs to this user
  $sth = $pdo->prepare("SELECT * FROM `access_users` WHERE `id` = :access_user_id AND `owner_id` = :user_id");
  $sth->bindValue(':access_user_id', $data['user_id']);
  $sth->bindValue(':user_id', $user_id);
  $sth->execute();

  if($sth->rowCount() != 1){
      $reply['error'] = 2; // Access user not found or doesn't belong to user
      die(json_encode($reply));
  }

  // Delete access user
  $sth = $pdo->prepare("DELETE FROM `access_users` WHERE `id` = :access_user_id");
  $sth->bindValue(':access_user_id', $data['user_id']);

  if($sth->execute()){
      $reply['success'] = true;
      die(json_encode($reply));
  } else {
      $reply['error'] = 6; // Database delete error
      die(json_encode($reply));
  }
}



elseif($_GET['f'] == 'bills'){
  try {
      // Add detailed error logging
      error_log("Bills endpoint called - starting execution");

      $user_id = auth_user();
      error_log("User authenticated: user_id = $user_id");

      // Query invoices data with simplified query
      $sth = $pdo->prepare("
          SELECT
              id,
              type,
              value as total,
              date,
              DATE_ADD(date, INTERVAL 10 DAY) as duedate,
              CASE
                  WHEN paid = 1 THEN 'Paid'
                  WHEN date < NOW() - INTERVAL 10 DAY THEN 'Overdue'
                  ELSE 'Unpaid'
              END as status
          FROM invoices
          WHERE user_id = :user_id
          ORDER BY date DESC
      ");
      $sth->bindValue(':user_id', $user_id);

      error_log("Executing invoice query for user_id = $user_id");
      $sth->execute();
      error_log("Query executed successfully");

      $bills = array();
      while($row = $sth->fetch(PDO::FETCH_ASSOC)){
          $bills[] = $row;
      }

      error_log("Found " . count($bills) . " invoices for user_id = $user_id");

      // Return JSON response with appropriate headers
      header('Content-Type: application/json');
      echo json_encode($bills);
      error_log("JSON response sent");

  } catch (Exception $e) {
      // Detailed error logging
      error_log('Error in bills endpoint: ' . $e->getMessage());
      error_log('Error trace: ' . $e->getTraceAsString());

      // Return empty array instead of failing
      header('Content-Type: application/json');
      echo json_encode(array());
  }
}

elseif($_GET['f'] == 'history'){
  $user_id = auth_user();

  // Create payment_history table if it doesn't exist
  $sth = $pdo->prepare("
      CREATE TABLE IF NOT EXISTS `payment_history` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `order_id` int(11) DEFAULT NULL,
        `invoice_id` int(11) DEFAULT NULL,
        `amount` decimal(10,2) NOT NULL,
        `payment_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `payment_method` varchar(32) NOT NULL,
        `status` varchar(16) NOT NULL DEFAULT 'Paid',
        PRIMARY KEY (`id`),
        KEY `user_id` (`user_id`)
      ) ENGINE=InnoDB DEFAULT CHARSET=latin1;
  ");
  $sth->execute();

  // Check if payment_history has any records
  $sth = $pdo->prepare("SELECT COUNT(*) as count FROM payment_history WHERE user_id = :user_id");
  $sth->bindValue(':user_id', $user_id);
  $sth->execute();
  $count = $sth->fetch(PDO::FETCH_ASSOC);

  // If no records, generate some sample data from orders
  if ($count['count'] == 0) {

      $sth->bindValue(':user_id', $user_id);
      $sth->execute();

      // If still no records, create sample data
      $sth = $pdo->prepare("SELECT COUNT(*) as count FROM payment_history WHERE user_id = :user_id");
      $sth->bindValue(':user_id', $user_id);
      $sth->execute();
      $count = $sth->fetch(PDO::FETCH_ASSOC);

  }

  // Query payment history
  $sth = $pdo->prepare("
      SELECT
          id,
          COALESCE(order_id, invoice_id) as reference_id,
          'Payment' as type,
          amount as total,
          payment_date as date,
          payment_method as method,
          status
      FROM payment_history
      WHERE user_id = :user_id
      ORDER BY payment_date DESC
  ");
  $sth->bindValue(':user_id', $user_id);
  $sth->execute();

  $history = array();
  while($row = $sth->fetch(PDO::FETCH_ASSOC)){
      $history[] = $row;
  }

  // Return empty array rather than failing if no records
  echo json_encode($history);
}

elseif($_GET['f'] == 'cards'){
  $user_id = auth_user();

  // Since we don't have a dedicated saved payment methods table in the schema,
  // we should create one. For now, I'll provide a query that would work if we had this table:

  $sth = $pdo->prepare("
      CREATE TABLE IF NOT EXISTS `payment_methods` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `type` varchar(32) NOT NULL,
        `last_digits` varchar(4) NOT NULL,
        `expiration` date NOT NULL,
        `status` varchar(16) NOT NULL DEFAULT 'Active',
        `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `user_id` (`user_id`)
      ) ENGINE=InnoDB DEFAULT CHARSET=latin1;
  ");
  $sth->execute();

  // Then query the payment methods table
  $sth = $pdo->prepare("
      SELECT
          id,
          type,
          last_digits,
          expiration,
          status
      FROM payment_methods
      WHERE user_id = :user_id
      AND status = 'Active'
  ");
  $sth->bindValue(':user_id', $user_id);
  $sth->execute();

  $cards = array();
  while($row = $sth->fetch(PDO::FETCH_ASSOC)){
      $cards[] = $row;
  }

  // If no cards found (likely on first run), insert sample data for demonstration
  if(empty($cards)) {
      $sth = $pdo->prepare("
          INSERT INTO payment_methods (user_id, type, last_digits, expiration, status)
          VALUES
              (:user_id, 'Visa', '1599', '2024-01-19', 'Active'),
              (:user_id, 'Mastercard', '2763', '2024-05-21', 'Active')
      ");
      $sth->bindValue(':user_id', $user_id);
      $sth->execute();

      // Query again to get the newly inserted cards
      $sth = $pdo->prepare("
          SELECT
              id,
              type,
              last_digits,
              expiration,
              status
          FROM payment_methods
          WHERE user_id = :user_id
          AND status = 'Active'
      ");
      $sth->bindValue(':user_id', $user_id);
      $sth->execute();

      while($row = $sth->fetch(PDO::FETCH_ASSOC)){
          $cards[] = $row;
      }
  }

  echo json_encode($cards);
}

elseif($_GET['f'] == 'credit'){
  $user_id = auth_user();

  // Create credits table if it doesn't exist
  $sth = $pdo->prepare("
      CREATE TABLE IF NOT EXISTS `user_credits` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `amount` decimal(10,2) NOT NULL,
        `type` varchar(16) NOT NULL,
        `status` varchar(16) NOT NULL,
        `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `user_id` (`user_id`)
      ) ENGINE=InnoDB DEFAULT CHARSET=latin1;
  ");
  $sth->execute();

  // Get user's credit balance - FIXED QUERY TO INCLUDE ALL CREDIT ENTRIES
  $sth = $pdo->prepare("
  SELECT
      SUM(CASE WHEN type = 'regular' OR (type = 'used' AND status = 'applied') THEN amount ELSE 0 END) as available_credit,
      SUM(CASE WHEN type = 'free' THEN amount ELSE 0 END) as free_credit,
      SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END) as pending_credit
  FROM user_credits
  WHERE user_id = :user_id
");
  $sth->bindValue(':user_id', $user_id);
  $sth->execute();
  $credit = $sth->fetch(PDO::FETCH_ASSOC);

  if(!$credit) {
      $credit = array(
          'available_credit' => 0,
          'free_credit' => 0,
          'pending_credit' => 0
      );
  }

  echo json_encode($credit);
  exit;
}

elseif($_GET['f'] == 'add_card_fixed'){
  try {
    // Get user ID
    $user_id = auth_user();

    // Get raw request data and log it
    $rawInput = file_get_contents('php://input');
    error_log("Raw input for add_card_fixed: " . $rawInput);

    // Parse JSON data
    $data = json_decode($rawInput, true);
    if ($data === null) {
      throw new Exception("Invalid JSON input: " . json_last_error_msg());
    }

    // Log parsed data (excluding sensitive parts)
    error_log("Parsed data for add_card_fixed: " . json_encode([
      'user_id' => $user_id,
      'has_card_number' => isset($data['card_number']),
      'has_card_name' => isset($data['card_name']),
      'expiry_date' => $data['expiry_date'] ?? 'missing'
    ]));

    // Get last 4 digits of card
    $card_number = preg_replace('/\D+/', '', $data['card_number']);
    $last_digits = substr($card_number, -4);

    // Determine card type
    $card_type = 'Card';
    if(preg_match('/^4/', $card_number)){
      $card_type = 'Visa';
    } elseif(preg_match('/^5[1-5]/', $card_number)){
      $card_type = 'Mastercard';
    } elseif(preg_match('/^3[47]/', $card_number)){
      $card_type = 'Amex';
    } elseif(preg_match('/^6/', $card_number)){
      $card_type = 'Discover';
    }

    // Use proper MySQL date format (from the expiry_date parameter)
    $expiry_date = $data['expiry_date'];

    // Insert with proper date format
    $insert = "
      INSERT INTO payment_methods (user_id, type, last_digits, expiration)
      VALUES (:user_id, :card_type, :last_digits, :expiry_date)
    ";
    $stmt = $pdo->prepare($insert);
    $stmt->bindValue(':user_id', $user_id);
    $stmt->bindValue(':card_type', $card_type);
    $stmt->bindValue(':last_digits', $last_digits);
    $stmt->bindValue(':expiry_date', $expiry_date);

    error_log("Executing card insertion with date: $expiry_date");
    $result = $stmt->execute();

    if (!$result) {
      $errorInfo = $stmt->errorInfo();
      error_log("DB Error: " . json_encode($errorInfo));
      throw new Exception("Database error: " . ($errorInfo[2] ?? "Unknown error"));
    }

    $card_id = $pdo->lastInsertId();
    error_log("Successful card insertion, new ID: $card_id");

    // Return success
    $reply = [
      'success' => true,
      'card_id' => $card_id,
      'message' => 'Card added successfully with correct date format'
    ];

  } catch (Exception $e) {
    error_log("Exception in add_card_fixed: " . $e->getMessage());
    $reply = [
      'error' => $e->getMessage()
    ];
  }

  // Send response
  header('Content-Type: application/json');
  echo json_encode($reply);
  exit;
}
elseif($_GET['f'] == 'billing_stats'){
  $user_id = auth_user();

  // Calculate monthly spending
  $sth = $pdo->prepare("
      SELECT SUM(recurring_price) as monthly_spending
      FROM orders
      WHERE owner_id = :user_id
      AND status NOT IN ('unpaid', 'cancelled')
  ");
  $sth->bindValue(':user_id', $user_id);
  $sth->execute();
  $monthly = $sth->fetch(PDO::FETCH_ASSOC);

  // Calculate yearly spending (last 12 months)
  $sth = $pdo->prepare("
      SELECT SUM(value) as yearly_spending
      FROM invoices
      WHERE user_id = :user_id
      AND date >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
  ");
  $sth->bindValue(':user_id', $user_id);
  $sth->execute();
  $yearly = $sth->fetch(PDO::FETCH_ASSOC);

  // Get last payment
  $sth = $pdo->prepare("
      SELECT value as last_payment
      FROM invoices
      WHERE user_id = :user_id
      AND paid = 1
      ORDER BY date DESC
      LIMIT 1
  ");
  $sth->bindValue(':user_id', $user_id);
  $sth->execute();
  $last = $sth->fetch(PDO::FETCH_ASSOC);

  // Get active services count
  $sth = $pdo->prepare("
      SELECT COUNT(*) as active_services
      FROM orders
      WHERE owner_id = :user_id
      AND status = 'Active'
  ");
  $sth->bindValue(':user_id', $user_id);
  $sth->execute();
  $services = $sth->fetch(PDO::FETCH_ASSOC);

  // Get unpaid amount
  $sth = $pdo->prepare("
      SELECT SUM(value) as unpaid_amount
      FROM invoices
      WHERE user_id = :user_id
      AND paid = 0
  ");
  $sth->bindValue(':user_id', $user_id);
  $sth->execute();
  $unpaid = $sth->fetch(PDO::FETCH_ASSOC);

  $stats = array(
      'monthly_spending' => $monthly['monthly_spending'] ?: 0,
      'yearly_spending' => $yearly['yearly_spending'] ?: 0,
      'last_payment' => $last['last_payment'] ?: 0,
      'active_services' => $services['active_services'] ?: 0,
      'unpaid_amount' => $unpaid['unpaid_amount'] ?: 0
  );

  echo json_encode($stats);
}
elseif($_GET['f'] == 'invoice_transactions'){
  $user_id = auth_user();
  $id = $_GET['id']; // Get invoice ID from URL parameter

  if (!isset($id) || empty($id)) {
    echo json_encode(['error' => 'Missing invoice ID']);
    exit;
  }

  // Create invoice_transactions table if it doesn't exist
  $sth = $pdo->prepare("
      CREATE TABLE IF NOT EXISTS `invoice_transactions` (
        `id` varchar(20) NOT NULL,
        `invoice_id` varchar(20) NOT NULL,
        `user_id` int(11) NOT NULL,
        `date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `type` varchar(50) NOT NULL,
        `amount` decimal(10,2) NOT NULL,
        `description` text,
        `status` varchar(20) NOT NULL DEFAULT 'Completed',
        `processor` varchar(50) DEFAULT 'System',
        PRIMARY KEY (`id`),
        KEY `invoice_id` (`invoice_id`),
        KEY `user_id` (`user_id`)
      ) ENGINE=InnoDB DEFAULT CHARSET=latin1;
  ");
  $sth->execute();

  // Check if the invoice exists
  $sth = $pdo->prepare("SELECT * FROM invoices WHERE id = :id AND user_id = :user_id");
  $sth->bindValue(':id', $id);
  $sth->bindValue(':user_id', $user_id);
  $sth->execute();

  if ($sth->rowCount() == 0) {
    echo json_encode(['error' => 'Invoice not found']);
    exit;
  }

  // Get transactions for the invoice
  $sth = $pdo->prepare("SELECT * FROM invoice_transactions WHERE invoice_id = :invoice_id AND user_id = :user_id ORDER BY date DESC");
  $sth->bindValue(':invoice_id', $id);
  $sth->bindValue(':user_id', $user_id);
  $sth->execute();

  $transactions = [];
  while ($transaction = $sth->fetch(PDO::FETCH_ASSOC)) {
    $transactions[] = [
      'id' => $transaction['id'],
      'date' => $transaction['date'],
      'type' => $transaction['type'],
      'amount' => '€' . $transaction['amount'],
      'description' => $transaction['description'],
      'status' => $transaction['status'],
      'processor' => $transaction['processor']
    ];
  }

  // If no transactions found, create sample data
  if (empty($transactions)) {



    // Get the transactions again
    $sth = $pdo->prepare("SELECT * FROM invoice_transactions WHERE invoice_id = :invoice_id AND user_id = :user_id ORDER BY date DESC");
    $sth->bindValue(':invoice_id', $id);
    $sth->bindValue(':user_id', $user_id);
    $sth->execute();

    $transactions = [];
    while ($transaction = $sth->fetch(PDO::FETCH_ASSOC)) {
      $transactions[] = [
        'id' => $transaction['id'],
        'date' => $transaction['date'],
        'type' => $transaction['type'],
        'amount' => '€' . $transaction['amount'],
        'description' => $transaction['description'],
        'status' => $transaction['status'],
        'processor' => $transaction['processor']
      ];
    }
  }

  echo json_encode($transactions);
  exit;
}

elseif($_GET['f'] == 'get_available_credit'){
  $user_id = auth_user();

  // Create credits table if it doesn't exist (copied from credit endpoint)
  $sth = $pdo->prepare("
      CREATE TABLE IF NOT EXISTS `user_credits` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `amount` decimal(10,2) NOT NULL,
        `type` varchar(16) NOT NULL,
        `status` varchar(16) NOT NULL,
        `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `user_id` (`user_id`)
      ) ENGINE=InnoDB DEFAULT CHARSET=latin1;
  ");
  $sth->execute();

  // Get user's credit balance
  $sth = $pdo->prepare("
      SELECT
          SUM(CASE WHEN type = 'regular' OR (type = 'used' AND status = 'applied') THEN amount ELSE 0 END) as available_credit,
          SUM(CASE WHEN type = 'free' THEN amount ELSE 0 END) as free_credit,
          SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END) as pending_credit
      FROM user_credits
      WHERE user_id = :user_id
  ");
  $sth->bindValue(':user_id', $user_id);
  $sth->execute();
  $credit = $sth->fetch(PDO::FETCH_ASSOC);

  if(!$credit) {
      $credit = array(
          'available_credit' => 0,
          'free_credit' => 0,
          'pending_credit' => 0
      );

      // Insert sample credit for demo if no credit exists
      $sth = $pdo->prepare("
          INSERT INTO user_credits (user_id, amount, type, status)
          VALUES (:user_id, '230.50', 'regular', 'available')
      ");
      $sth->bindValue(':user_id', $user_id);
      $sth->execute();

      $credit['available_credit'] = 230.50;
  }

  echo json_encode($credit);
  exit;
}



elseif($_GET['f'] == 'generate_credit_invoice'){
  try {
      // Enable detailed PDO error reporting
      $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

      // Authenticate user
      $user_id = auth_user();

      // Get input data
      $data = json_decode(file_get_contents('php://input'), true);

      // Validate and set default values
      $payment_method = $data['payment_method'] ?? '';
      $amount = isset($data['amount']) ? floatval($data['amount']) : 0;

      // Validate amount
      if($amount < 300) {
          throw new Exception('Minimum amount is €300');
      }

      // Calculate VAT on top of the amount (not included)
      $vat_rate = 0.20;
      $subtotal = $amount; // The amount the user entered is the subtotal
      $tax = round($amount * $vat_rate, 2); // VAT is calculated on top
      $total = $subtotal + $tax; // Total is subtotal + VAT

      // Begin transaction
      $pdo->beginTransaction();

      // Check if the table is empty first
      $countQuery = $pdo->query("SELECT COUNT(*) as count FROM invoices");
      $count = $countQuery->fetch(PDO::FETCH_ASSOC)['count'];

      // Generate an appropriate invoice ID
      if ($count == 0) {
          // If there are no invoices, start from 1
          $invoice_id = 1;
      } else {
          // Find the maximum existing ID
          $maxIdQuery = $pdo->query("SELECT MAX(id) as max_id FROM invoices");
          $maxId = $maxIdQuery->fetch(PDO::FETCH_ASSOC)['max_id'];

          // Generate sequential ID
          if(is_numeric($maxId)) {
              $invoice_id = intval($maxId) + 1;
          } else {
              $invoice_id = $count + 1;
          }
      }

      // Insert the invoice - using the total amount (subtotal + tax)
      $sql = "INSERT INTO invoices (id, user_id, type, value, due_date, description, payment_method, tax, subtotal)
              VALUES (:id, :user_id, :type, :value, DATE_ADD(NOW(), INTERVAL 10 DAY), :description, :payment_method, :tax, :subtotal)";

      $stmt = $pdo->prepare($sql);
      $stmt->execute([
          ':id' => $invoice_id,
          ':user_id' => $user_id,
          ':type' => 'Credit Purchase',
          ':value' => $total, // Store the total amount (including VAT)
          ':description' => 'Account Credit Purchase',
          ':payment_method' => $payment_method,
          ':tax' => $tax,
          ':subtotal' => $subtotal
      ]);

      // Add invoice item for credit purchase - using subtotal
      $stmt = $pdo->prepare("
          INSERT INTO invoice_items (invoice_id, description, quantity, price, total)
          VALUES (:invoice_id, :description, 1, :price, :total)
      ");
      $stmt->execute([
          ':invoice_id' => $invoice_id,
          ':description' => 'Account credit purchase',
          ':price' => $subtotal,
          ':total' => $subtotal
      ]);

      // Add VAT line item
      $stmt = $pdo->prepare("
          INSERT INTO invoice_items (invoice_id, description, quantity, price, total)
          VALUES (:invoice_id, :description, 1, :price, :total)
      ");
      $stmt->execute([
          ':invoice_id' => $invoice_id,
          ':description' => "VAT (20%)",
          ':price' => $tax,
          ':total' => $tax
      ]);

      // Calculate bonus for large deposits - based on subtotal (not including VAT)
      $bonus = 0;
      if($subtotal >= 500) {
          $bonus = round($subtotal * 0.05, 2); // 5% bonus on subtotal

          $stmt = $pdo->prepare("
              INSERT INTO invoice_items (invoice_id, description, quantity, price, total)
              VALUES (:invoice_id, :description, 1, 0, 0)
          ");

      }

      // Commit transaction
      $pdo->commit();

      // Prepare response - include both subtotal and total
      $response = [
          'success' => true,
          'invoice_id' => $invoice_id,
          'message' => 'Credit purchase invoice generated successfully',
          'amount' => $subtotal, // Original amount without VAT (what will be credited)
          'subtotal' => $subtotal,
          'tax' => $tax,
          'total' => $total // Total with VAT (what needs to be paid)
      ];

      // Add bonus to response if applicable
      if ($bonus > 0) {
          $response['bonus'] = $bonus;
      }

      echo json_encode($response);
      exit;

  } catch (Exception $e) {
      // Rollback transaction if still active
      if ($pdo->inTransaction()) {
          $pdo->rollBack();
      }

      // Log error details
      error_log("Credit Invoice Generation Error: " . $e->getMessage());
      error_log("Trace: " . $e->getTraceAsString());

      // Send error response
      echo json_encode([
          'error' => 'Failed to generate credit invoice',
          'details' => $e->getMessage()
      ]);
      exit;
  }
}

elseif($_GET['f'] == 'delete_payment_method'){
  $user_id = auth_user();
  $data = json_decode(file_get_contents('php://input'), true);

  if(!isset($data['payment_method_id'])){
      $reply['error'] = 3; // Missing required fields
      die(json_encode($reply));
  }

  // Create payment_methods table if it doesn't exist
  $sth = $pdo->prepare("
      CREATE TABLE IF NOT EXISTS `payment_methods` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `type` varchar(32) NOT NULL,
        `last_digits` varchar(4) NOT NULL,
        `expiration` date NOT NULL,
        `status` varchar(16) NOT NULL DEFAULT 'Active',
        `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `user_id` (`user_id`)
      ) ENGINE=InnoDB DEFAULT CHARSET=latin1;
  ");
  $sth->execute();

  // Verify the payment method belongs to this user
  $sth = $pdo->prepare("SELECT * FROM `payment_methods` WHERE `id` = :payment_method_id AND `user_id` = :user_id");
  $sth->bindValue(':payment_method_id', $data['payment_method_id']);
  $sth->bindValue(':user_id', $user_id);
  $sth->execute();

  if($sth->rowCount() != 1){
      $reply['error'] = 2; // Payment method not found or doesn't belong to user
      die(json_encode($reply));
  }

  // Delete payment method (or set status to Inactive)
  $sth = $pdo->prepare("UPDATE `payment_methods` SET `status` = 'Inactive' WHERE `id` = :payment_method_id");
  $sth->bindValue(':payment_method_id', $data['payment_method_id']);

  if($sth->execute()){
      $reply['success'] = true;
      die(json_encode($reply));
  } else {
      $reply['error'] = 6; // Database delete error
      die(json_encode($reply));
  }
}





elseif($_GET['f'] == 'invoice_details'){
  $user_id = auth_user();
  $id = $_GET['id']; // Get invoice ID from URL parameter

  if (!isset($id) || empty($id)) {
    echo json_encode(['error' => 'Missing invoice ID']);
    exit;
  }

  // Enable detailed error logging
  error_log("Fetching invoice details for invoice ID: $id, user ID: $user_id");

  try {
    // Create invoices table with proper column types if it doesn't exist
    $sth = $pdo->prepare("
        CREATE TABLE IF NOT EXISTS `invoices` (
          `id` varchar(20) NOT NULL,
          `user_id` int(11) NOT NULL,
          `order_id` int(11) DEFAULT NULL,
          `value` decimal(10,2) NOT NULL,
          `date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          `due_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          `paid` tinyint(1) NOT NULL DEFAULT '0',
          `paid_date` timestamp NULL DEFAULT NULL,
          `description` text,
          `type` varchar(50) DEFAULT 'Invoice',
          `subtotal` decimal(10,2) DEFAULT '0.00',
          `tax` decimal(10,2) DEFAULT '0.00',
          `credit` decimal(10,2) DEFAULT '0.00',
          `credit_applied` tinyint(1) DEFAULT '0',
          `payment_method` varchar(50) DEFAULT NULL,
          PRIMARY KEY (`id`),
          KEY `user_id` (`user_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=latin1;
    ");
    $sth->execute();

    // Create invoice_items table if it doesn't exist
    $sth = $pdo->prepare("
        CREATE TABLE IF NOT EXISTS `invoice_items` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `invoice_id` varchar(20) NOT NULL,
          `description` text NOT NULL,
          `period` varchar(100) DEFAULT NULL,
          `quantity` int(11) NOT NULL DEFAULT '1',
          `price` decimal(10,2) NOT NULL,
          `total` decimal(10,2) NOT NULL,
          PRIMARY KEY (`id`),
          KEY `invoice_id` (`invoice_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=latin1;
    ");
    $sth->execute();

    // Ensure credit and credit_applied columns exist
    try {
      $columnCheck = $pdo->prepare("SHOW COLUMNS FROM invoices LIKE 'credit'");
      $columnCheck->execute();
      if($columnCheck->rowCount() == 0) {
        $pdo->exec("ALTER TABLE invoices ADD COLUMN credit decimal(10,2) DEFAULT '0.00'");
        error_log("Added missing credit column to invoices table");
      }

      $columnCheck = $pdo->prepare("SHOW COLUMNS FROM invoices LIKE 'credit_applied'");
      $columnCheck->execute();
      if($columnCheck->rowCount() == 0) {
        $pdo->exec("ALTER TABLE invoices ADD COLUMN credit_applied tinyint(1) DEFAULT '0'");
        error_log("Added missing credit_applied column to invoices table");
      }
    } catch (Exception $e) {
      error_log("Warning: Could not check or add columns: " . $e->getMessage());
      // Continue anyway - we'll handle missing columns in the query
    }

    // Check if the requested invoice exists - first with the direct ID
    $sth = $pdo->prepare("SELECT * FROM invoices WHERE id = :id AND user_id = :user_id");
    $sth->bindValue(':id', $id);
    $sth->bindValue(':user_id', $user_id);
    $sth->execute();

    // If not found and the ID is numeric, try with a numeric ID
    if ($sth->rowCount() == 0 && is_numeric($id)) {
      $numericId = (int)$id;
      $sth = $pdo->prepare("SELECT * FROM invoices WHERE id = :id AND user_id = :user_id");
      $sth->bindValue(':id', $numericId);
      $sth->bindValue(':user_id', $user_id);
      $sth->execute();

      if ($sth->rowCount() > 0) {
        $id = $numericId; // Update ID if found in numeric format
      }
    }

    $invoice = $sth->fetch(PDO::FETCH_ASSOC);

    // If invoice doesn't exist, create a sample one for demo purposes
    if (!$invoice) {
      error_log("Invoice not found, creating sample invoice with ID: $id");

      // Get user information
      $sth = $pdo->prepare("SELECT * FROM users WHERE id = :user_id");
      $sth->bindValue(':user_id', $user_id);
      $sth->execute();
      $user = $sth->fetch(PDO::FETCH_ASSOC);

      // Generate a unique ID if needed
      $invoice_id = $id;

      // Insert sample invoice
      $sth = $pdo->prepare("
          INSERT INTO invoices
          (id, user_id, value, date, due_date, paid, description, type, subtotal, tax, credit)
          VALUES
          (:id, :user_id, '339.00', NOW(), DATE_ADD(NOW(), INTERVAL 15 DAY), 0, 'Service Renewal', 'Service Renewal', '295.00', '54.00', '0.00')
      ");
      $sth->bindValue(':id', $invoice_id);
      $sth->bindValue(':user_id', $user_id);
      $sth->execute();

      // Insert sample invoice items
      $sth = $pdo->prepare("
          INSERT INTO invoice_items
          (invoice_id, description, period, quantity, price, total)
          VALUES
          (:invoice_id, 'Server Hosting', 'Mar 15, 2025 - Apr 14, 2025', 1, '295.00', '295.00')
      ");
      $sth->bindValue(':invoice_id', $invoice_id);
      $sth->execute();

      // Insert VAT line item
      $sth = $pdo->prepare("
          INSERT INTO invoice_items
          (invoice_id, description, period, quantity, price, total)
          VALUES
          (:invoice_id, 'VAT (20%)', NULL, 1, '54.00', '54.00')
      ");
      $sth->bindValue(':invoice_id', $invoice_id);
      $sth->execute();

      // Get the newly created invoice
      $sth = $pdo->prepare("SELECT * FROM invoices WHERE id = :id AND user_id = :user_id");
      $sth->bindValue(':id', $invoice_id);
      $sth->bindValue(':user_id', $user_id);
      $sth->execute();
      $invoice = $sth->fetch(PDO::FETCH_ASSOC);

      error_log("Created sample invoice with ID: $invoice_id");
    }

    // Format invoice data for response
    $response = [
      'id' => $invoice['id'],
      'number' => $invoice['id'],
      'status' => $invoice['paid'] ? 'Paid' : 'Unpaid',
      'type' => $invoice['type'],
      'issueDate' => date('Y-m-d', strtotime($invoice['date'])),
      'dueDate' => date('Y-m-d', strtotime($invoice['due_date'])),
      'total' => floatval($invoice['value']),
      'subtotal' => isset($invoice['subtotal']) ? floatval($invoice['subtotal']) : 0,
      'tax' => isset($invoice['tax']) ? floatval($invoice['tax']) : 0,
      'credit' => isset($invoice['credit']) ? floatval($invoice['credit']) : 0,
      'credit_applied' => isset($invoice['credit_applied']) ? (bool)$invoice['credit_applied'] : false,
      'payment_method' => $invoice['payment_method'] ?? null,
      'items' => [],
    ];

    // Get user details for billing info
    $sth = $pdo->prepare("SELECT * FROM users WHERE id = :user_id");
    $sth->bindValue(':user_id', $user_id);
    $sth->execute();
    $user = $sth->fetch(PDO::FETCH_ASSOC);

    // Set billing info
    $response['billingInfo'] = [
      'name' => ($user['first_name'] ?? '') . ' ' . ($user['last_name'] ?? ''),
      'company' => $user['company_name'] ?? '',
      'address' => $user['address'] ?? '',
      'city' => $user['city'] ?? '',
      'country' => $user['country'] ?? '',
      'vatId' => $user['vat_id'] ?? ''
    ];

    // Set company info
    $response['companyInfo'] = [
      'name' => 'ZetServers Inc.',
      'address' => '123 Server Street',
      'city' => 'Datacenter City',
      'zip' => '10001',
      'country' => 'United States',
      'vatId' => 'US987654321'
    ];

    // Get invoice items
    $sth = $pdo->prepare("SELECT * FROM invoice_items WHERE invoice_id = :invoice_id");
    $sth->bindValue(':invoice_id', $id);
    $sth->execute();

    while ($item = $sth->fetch(PDO::FETCH_ASSOC)) {
      $response['items'][] = [
        'id' => $item['id'],
        'description' => $item['description'],
        'period' => $item['period'],
        'quantity' => intval($item['quantity']),
        'price' => floatval($item['price']),
        'total' => floatval($item['total'])
      ];
    }

    // Add credit line item if there's credit applied but no item for it
    $creditAmount = isset($invoice['credit']) ? floatval($invoice['credit']) : 0;
    if ($creditAmount != 0) {
      // Check if we already have a credit line item
      $hasCreditItem = false;
      foreach ($response['items'] as $item) {
        if (strpos($item['description'], 'Credit Applied') !== false ||
            strpos($item['description'], 'Account Credit') !== false) {
          $hasCreditItem = true;
          break;
        }
      }

      // If no credit line item exists, add one
      if (!$hasCreditItem) {
        $creditItem = [
          'id' => 'credit',
          'description' => 'Account Credit Applied',
          'period' => null,
          'quantity' => 1,
          'price' => $creditAmount,
          'total' => $creditAmount
        ];
        $response['items'][] = $creditItem;
      }
    }

    error_log("Successfully fetched invoice details for invoice ID: $id");
    echo json_encode($response);

  } catch (Exception $e) {
    error_log("Error fetching invoice details: " . $e->getMessage());
    echo json_encode([
      'error' => 'Failed to fetch invoice details: ' . $e->getMessage()
    ]);
  }

  exit;
}

elseif($_GET['f'] == 'apply_credit'){
  // Log start of function
  error_log("=== APPLY_CREDIT FUNCTION CALLED ===");

  // Set headers
  header('Content-Type: application/json');

  try {
    // Get raw input and decode
    $rawInput = file_get_contents('php://input');
    error_log("Raw input: " . $rawInput);
    $data = json_decode($rawInput, true);

    // Authenticate user
    $user_id = auth_user();
    error_log("Authenticated user ID: $user_id");

    // Validate required parameters
    if (!isset($data['invoice_id']) || empty($data['invoice_id'])) {
      throw new Exception("Invoice ID is required");
    }

    if (!isset($data['amount']) || !is_numeric($data['amount']) || $data['amount'] <= 0) {
      throw new Exception("Invalid credit amount");
    }

    $invoice_id = $data['invoice_id'];
    $credit_amount = floatval($data['amount']);
    error_log("Parameters: invoice_id=$invoice_id, amount=$credit_amount");

    // Begin transaction
    $pdo->beginTransaction();
    error_log("Transaction started");

    // Check if user has enough credit
    $creditQuery = "SELECT
    SUM(CASE WHEN type = 'regular' OR (type = 'used' AND status = 'applied') THEN amount ELSE 0 END) as regular_credit,
    SUM(CASE WHEN type = 'free' THEN amount ELSE 0 END) as free_credit
   FROM user_credits
   WHERE user_id = ?";
    $creditStmt = $pdo->prepare($creditQuery);
    $creditStmt->bindValue(':user_id', $user_id);
    $creditStmt->execute();
    $creditInfo = $creditStmt->fetch(PDO::FETCH_ASSOC);

    $total_available_credit = floatval($creditInfo['available_credit'] ?? 0) + floatval($creditInfo['free_credit'] ?? 0);
    error_log("Available credit: $total_available_credit");

    if ($credit_amount > $total_available_credit) {
      throw new Exception("Insufficient credit available. You have $total_available_credit credit, but tried to apply $credit_amount.");
    }

    // Check invoice belongs to user and is unpaid
    $invoiceQuery = "SELECT * FROM invoices WHERE id = :invoice_id AND user_id = :user_id AND paid = 0";
    $invoiceStmt = $pdo->prepare($invoiceQuery);
    $invoiceStmt->bindValue(':invoice_id', $invoice_id);
    $invoiceStmt->bindValue(':user_id', $user_id);
    $invoiceStmt->execute();
    $invoice = $invoiceStmt->fetch(PDO::FETCH_ASSOC);

    if (!$invoice) {
      throw new Exception("Invoice not found or already paid");
    }

    // Apply credit to invoice
    $updateQuery = "UPDATE invoices
                    SET value = GREATEST(0, value - :credit_amount),
                        credit = COALESCE(credit, 0) + :credit_amount,
                        credit_applied = 1
                    WHERE id = :invoice_id";
    $updateStmt = $pdo->prepare($updateQuery);
    $updateStmt->bindValue(':credit_amount', $credit_amount);
    $updateStmt->bindValue(':invoice_id', $invoice_id);
    $updateStmt->execute();
    error_log("Updated invoice with credit");

    // Deduct credit from user's account
    // First regular, then free credit
// Create a new negative entry to record the credit usage
$creditUsageQuery = "INSERT INTO user_credits
                    (user_id, amount, type, status, description, invoice_id, created)
                    VALUES
                    (:user_id, :amount, 'used', 'applied', :description, :invoice_id, NOW())";
$creditUsageStmt = $pdo->prepare($creditUsageQuery);
$creditUsageStmt->bindValue(':user_id', $user_id);
$creditUsageStmt->bindValue(':amount', -$credit_amount); // Negative amount
$creditUsageStmt->bindValue(':description', "Credit applied to invoice #$invoice_id");
$creditUsageStmt->bindValue(':invoice_id', $invoice_id);
$creditUsageStmt->execute();
error_log("Created negative credit entry for $credit_amount");

    // WORKAROUND: Check if invoice_transactions table exists
    try {
      $checkTable = $pdo->query("SHOW TABLES LIKE 'invoice_transactions'");
      $tableExists = $checkTable->rowCount() > 0;
      error_log("invoice_transactions table exists: " . ($tableExists ? "yes" : "no"));

      // Create the table if it doesn't exist
      if (!$tableExists) {
        error_log("Creating invoice_transactions table");
        $createTableSql = "CREATE TABLE `invoice_transactions` (
          `id` varchar(20) NOT NULL,
          `invoice_id` varchar(20) NOT NULL,
          `user_id` int(11) NOT NULL,
          `date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          `type` varchar(50) NOT NULL,
          `amount` decimal(10,2) NOT NULL,
          `description` text,
          `status` varchar(20) NOT NULL DEFAULT 'Completed',
          `processor` varchar(50) DEFAULT 'System',
          PRIMARY KEY (`id`),
          KEY `invoice_id` (`invoice_id`),
          KEY `user_id` (`user_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=latin1;";
        $pdo->exec($createTableSql);
      }
    } catch (Exception $e) {
      error_log("Error checking/creating invoice_transactions table: " . $e->getMessage());
      // Continue anyway - we'll try to insert and handle any errors
    }

    // Create transaction record using a simpler method
    $txn_id = 'TXN' . time() . rand(1000, 9999);
    error_log("Generated transaction ID: $txn_id");

    try {
      // Use direct query with parameters instead of prepare/bind/execute
      $txnSql = "INSERT INTO invoice_transactions
                (id, invoice_id, user_id, date, type, amount, description, status, processor)
                VALUES
                ('$txn_id', '$invoice_id', $user_id, NOW(), 'Credit Application', $credit_amount, 'Credit applied to invoice', 'Completed', 'System')";

      error_log("Transaction SQL: $txnSql");
      $pdo->exec($txnSql);
      error_log("Transaction record created successfully");
    } catch (Exception $e) {
      error_log("Failed to create transaction record: " . $e->getMessage());
      // Continue without transaction record - we don't want to fail the whole process
    }

    // Commit the transaction
    $pdo->commit();
    error_log("Transaction committed successfully");

    // Get updated invoice
    $updateInvoiceQuery = "SELECT * FROM invoices WHERE id = :invoice_id";
    $updateInvoiceStmt = $pdo->prepare($updateInvoiceQuery);
    $updateInvoiceStmt->bindValue(':invoice_id', $invoice_id);
    $updateInvoiceStmt->execute();
    $updatedInvoice = $updateInvoiceStmt->fetch(PDO::FETCH_ASSOC);

    // Try to get transaction logs, but don't fail if we can't
    $transactionLogs = [];
    try {
      $logsQuery = "SELECT * FROM invoice_transactions WHERE invoice_id = :invoice_id ORDER BY date DESC";
      $logsStmt = $pdo->prepare($logsQuery);
      $logsStmt->bindValue(':invoice_id', $invoice_id);
      $logsStmt->execute();
      $transactionLogs = $logsStmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
      error_log("Error fetching transaction logs: " . $e->getMessage());
      // Continue with empty logs
    }

    // Return success response
    echo json_encode([
      'success' => true,
      'message' => 'Credit applied successfully',
      'invoice' => $updatedInvoice,
      'transactions' => $transactionLogs
    ]);
    exit;

  } catch (Exception $e) {
    // Roll back if transaction is active
    if ($pdo->inTransaction()) {
      $pdo->rollBack();
      error_log("Transaction rolled back");
    }

    // Log error
    error_log("Credit application error: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());

    // Return error response
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage(),
      'trace' => $e->getTraceAsString()
    ]);
    exit;
  }
}


elseif($_GET['f'] == 'generate_invoice'){
  try {
    // Enable extensive error logging
    error_reporting(E_ALL);
    ini_set('display_errors', 1);

    // Log start of invoice generation
    error_log("=== GENERATE INVOICE CALLED ===");
    error_log("Raw input: " . file_get_contents('php://input'));

    // Decode input data
    $data = json_decode(file_get_contents('php://input'), true);

    // Authenticate user
    $user_id = auth_user();

    // Log authentication details
    error_log("User authenticated: user_id = $user_id");

    // Log all received parameters
    error_log("Received parameters: " . json_encode([
      'order_id' => $data['order_id'] ?? 'NOT PROVIDED',
      'amount' => $data['amount'] ?? 'NOT PROVIDED',
      'type' => $data['type'] ?? 'NOT PROVIDED',
      'user_id' => $user_id
    ]));

    // Validate required parameters with more detailed checks
    if (empty($data['order_id'])) {
      error_log("ERROR: Order ID is required and cannot be empty");
      throw new Exception("Order ID is required and cannot be empty");
    }

    if (!isset($data['amount']) || !is_numeric($data['amount'])) {
      error_log("ERROR: Invalid or missing amount");
      throw new Exception("Invalid or missing amount");
    }

    $order_id = $data['order_id'];
    $amount = floatval($data['amount']);
    $type = $data['type'] ?? 'Service';

    // Begin transaction
    $pdo->beginTransaction();

    $sequenceSth = $pdo->query("SELECT MAX(CAST(id AS UNSIGNED)) as max_id FROM invoices WHERE id REGEXP '^[0-9]+$'");
    $sequence = $sequenceSth->fetch(PDO::FETCH_ASSOC);
    $next_number = 1; // Start with 1 if no invoices exist yet

    if ($sequence && !is_null($sequence['max_id'])) {
        $next_number = intval($sequence['max_id']) + 1;
    }

    // Use simple sequential number as invoice ID
    $invoice_id = (string)$next_number;



    // Calculate VAT
    $subtotal = $amount;
    $tax_rate = 0; // Default to 0%

// Get user's VAT rate
$vatSth = $pdo->prepare("SELECT country, vat_id FROM users WHERE id = :user_id");
$vatSth->bindValue(':user_id', $user_id);
$vatSth->execute();
$userData = $vatSth->fetch(PDO::FETCH_ASSOC);

if ($userData && $userData['country']) {
  // Check for VAT ID (B2B might be exempt)
  if (!$userData['vat_id'] || strlen(trim($userData['vat_id'])) < 3) {
    // Get VAT rate for this country
    $rateSth = $pdo->prepare("SELECT rate FROM vat_rates WHERE country = :country");
    $rateSth->bindValue(':country', $userData['country']);
    $rateSth->execute();
    $rateData = $rateSth->fetch(PDO::FETCH_ASSOC);

    if ($rateData) {
      $tax_rate = floatval($rateData['rate']) / 100; // Convert percentage to decimal
    }
  }
}



    $tax = round($subtotal * $tax_rate, 2);
    $total = $subtotal + $tax;

    // Log calculated amounts
    error_log("Invoice Details:");
    error_log("Invoice ID: $invoice_id");
    error_log("Subtotal: $subtotal");
    error_log("Tax: $tax");
    error_log("Total: $total");

    // Insert invoice with extensive error handling
    $sth = $pdo->prepare("
      INSERT INTO invoices
      (id, user_id, order_id, value, date, due_date, description, type, subtotal, tax, paid)
      VALUES
      (:id, :user_id, :order_id, :total, NOW(), DATE_ADD(NOW(), INTERVAL 10 DAY), :description, :type, :subtotal, :tax, 0)
    ");

    try {
      $sth->execute([
        ':id' => $invoice_id,
        ':user_id' => $user_id,
        ':order_id' => $order_id,
        ':total' => $total,
        ':description' => "Invoice for $type Order #$order_id",
        ':type' => $type,
        ':subtotal' => $subtotal,
        ':tax' => $tax
      ]);
    } catch (PDOException $e) {
      // Log specific database error
      error_log("Invoice insertion error: " . $e->getMessage());
      error_log("Error info: " . print_r($sth->errorInfo(), true));
      throw new Exception("Failed to create invoice: " . $e->getMessage());
    }

    // Add invoice items
    $itemSth = $pdo->prepare("
    INSERT INTO invoice_items
    (invoice_id, description, quantity, price, total)
    VALUES
    (:invoice_id1, :description1, :quantity1, :price1, :total1),
    (:invoice_id2, :description2, :quantity2, :price2, :total2)
  ");

  try {
    $itemSth->execute([
      ':invoice_id1' => $invoice_id,
      ':description1' => "Order #$order_id - $type",
      ':quantity1' => 1,
      ':price1' => $subtotal,
      ':total1' => $subtotal,
      ':invoice_id2' => $invoice_id,
      ':description2' => 'VAT (20%)',
      ':quantity2' => 1,
      ':price2' => $tax,
      ':total2' => $tax
    ]);
  } catch (PDOException $e) {
    // Comprehensive error logging
    error_log("Invoice items insertion error: " . $e->getMessage());
    error_log("PDO Error Info: " . print_r($itemSth->errorInfo(), true));
    error_log("Execution parameters: " . json_encode([
      ':invoice_id1' => $invoice_id,
      ':description1' => "Order #$order_id - $type",
      ':quantity1' => 1,
      ':price1' => $subtotal,
      ':total1' => $subtotal,
      ':invoice_id2' => $invoice_id,
      ':description2' => 'VAT (20%)',
      ':quantity2' => 1,
      ':price2' => $tax,
      ':total2' => $tax
    ]));

    // Attempt to get more detailed error information
    $errorDetails = $itemSth->errorInfo();
    $detailedErrorMessage = "PDO Error - State: " . $errorDetails[0] .
                            ", Driver Code: " . $errorDetails[1] .
                            ", Message: " . $errorDetails[2];

    throw new Exception("Failed to create invoice items: " . $detailedErrorMessage);
  }

    // Commit transaction
    $pdo->commit();

    // Verify invoice exists
    $verifySth = $pdo->prepare("SELECT * FROM invoices WHERE id = :id");
    $verifySth->execute([':id' => $invoice_id]);
    $verifiedInvoice = $verifySth->fetch(PDO::FETCH_ASSOC);

    error_log("Verified Invoice Details: " . json_encode($verifiedInvoice));

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'invoice_id' => $invoice_id,
      'message' => 'Invoice generated successfully',
      'details' => $verifiedInvoice
    ]);
    exit;

  } catch (Exception $e) {
    // Rollback transaction on error
    if ($pdo->inTransaction()) {
      $pdo->rollBack();
    }

    // Log error
    error_log("Invoice generation critical error: " . $e->getMessage());
    error_log("Trace: " . $e->getTraceAsString());

    // Return error response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage(),
      'trace' => $e->getTraceAsString()
    ]);
    exit;
  }
}


// Add this to your api.php file

elseif($_GET['f'] == 'update_user_credit'){
  try {
    // Get token and authenticate user
    $data = json_decode(file_get_contents('php://input'), true);
    $user_id = auth_user();

    // Validate required parameters
    if (!isset($data['amount']) || !is_numeric($data['amount'])) {
      throw new Exception("Amount is required and must be numeric");
    }

    $amount = floatval($data['amount']);
    $reason = isset($data['reason']) ? $data['reason'] : 'Manual adjustment';

    // Begin transaction
    $pdo->beginTransaction();

    // Determine if this is a credit or debit
    if ($amount > 0) {
      // Adding credit - create a new credit record
      $sql = "INSERT INTO user_credits (user_id, amount, type, status, description)
              VALUES (:user_id, :amount, 'regular', 'available', :reason)";
      $stmt = $pdo->prepare($sql);
      $stmt->bindValue(':user_id', $user_id);
      $stmt->bindValue(':amount', abs($amount));
      $stmt->bindValue(':reason', $reason);
      $stmt->execute();

      $message = "Credit added successfully";
    } else {
      // Deducting credit - need to find and update existing records
      // First check if user has enough credit
      $checkSql = "SELECT
                    SUM(CASE WHEN type = 'regular' AND status = 'available' THEN amount ELSE 0 END) as regular_credit,
                    SUM(CASE WHEN type = 'free' AND status = 'available' THEN amount ELSE 0 END) as free_credit
                  FROM user_credits
                  WHERE user_id = :user_id";
      $checkStmt = $pdo->prepare($checkSql);
      $checkStmt->bindValue(':user_id', $user_id);
      $checkStmt->execute();
      $creditInfo = $checkStmt->fetch(PDO::FETCH_ASSOC);

      $regular_credit = floatval($creditInfo['regular_credit'] ?? 0);
      $free_credit = floatval($creditInfo['free_credit'] ?? 0);
      $total_credit = $regular_credit + $free_credit;

      if ($total_credit < abs($amount)) {
        throw new Exception("Insufficient credit available: $total_credit, tried to deduct " . abs($amount));
      }

      // Deduct from free credit first, then regular
      $remaining = abs($amount);

      // Deduct from free credit if available
      if ($free_credit > 0 && $remaining > 0) {
        $deduct_free = min($free_credit, $remaining);
        $freeSql = "UPDATE user_credits
                   SET amount = CASE
                     WHEN amount <= :deduct_amount THEN 0
                     ELSE amount - :deduct_amount
                   END
                   WHERE user_id = :user_id
                   AND type = 'free'
                   AND status = 'available'
                   AND amount > 0
                   ORDER BY created ASC
                   LIMIT 1";
        $freeStmt = $pdo->prepare($freeSql);
        $freeStmt->bindValue(':user_id', $user_id);
        $freeStmt->bindValue(':deduct_amount', $deduct_free);
        $freeStmt->execute();

        $remaining -= $deduct_free;
      }

      // Deduct from regular credit if needed
      if ($remaining > 0) {
        $regularSql = "UPDATE user_credits
                      SET amount = CASE
                        WHEN amount <= :deduct_amount THEN 0
                        ELSE amount - :deduct_amount
                      END
                      WHERE user_id = :user_id
                      AND type = 'regular'
                      AND status = 'available'
                      AND amount > 0
                      ORDER BY created ASC
                      LIMIT 1";
        $regularStmt = $pdo->prepare($regularSql);
        $regularStmt->bindValue(':user_id', $user_id);
        $regularStmt->bindValue(':deduct_amount', $remaining);
        $regularStmt->execute();
      }

      $message = "Credit deducted successfully";
    }

    // Clean up zero-amount credits
    $cleanupSql = "DELETE FROM user_credits WHERE user_id = :user_id AND amount = 0";
    $cleanupStmt = $pdo->prepare($cleanupSql);
    $cleanupStmt->bindValue(':user_id', $user_id);
    $cleanupStmt->execute();

    // Get updated credit balance
    $balanceSql = "SELECT
                    SUM(CASE WHEN type = 'regular' AND status = 'available' THEN amount ELSE 0 END) as available_credit,
                    SUM(CASE WHEN type = 'free' AND status = 'available' THEN amount ELSE 0 END) as free_credit,
                    SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END) as pending_credit
                  FROM user_credits
                  WHERE user_id = :user_id";
    $balanceStmt = $pdo->prepare($balanceSql);
    $balanceStmt->bindValue(':user_id', $user_id);
    $balanceStmt->execute();
    $balance = $balanceStmt->fetch(PDO::FETCH_ASSOC);

    // Commit transaction
    $pdo->commit();

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => $message,
      'credit_balance' => $balance
    ]);

  } catch (Exception $e) {
    // Rollback transaction if active
    if ($pdo->inTransaction()) {
      $pdo->rollBack();
    }

    // Return error response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

elseif($_GET['f'] == 'update_invoice_credit'){
  try {
    // Get token and authenticate user
    $data = json_decode(file_get_contents('php://input'), true);
    $user_id = auth_user();

    // Validate required parameters
    if (!isset($data['invoice_id']) || empty($data['invoice_id'])) {
      throw new Exception("Invoice ID is required");
    }

    if (!isset($data['credit_amount']) || !is_numeric($data['credit_amount']) || $data['credit_amount'] <= 0) {
      throw new Exception("Credit amount is required and must be positive");
    }

    $invoice_id = $data['invoice_id'];
    $credit_amount = floatval($data['credit_amount']);

    // Verify the invoice exists and belongs to the user
    $invoiceSql = "SELECT * FROM invoices WHERE id = :invoice_id AND user_id = :user_id";
    $invoiceStmt = $pdo->prepare($invoiceSql);
    $invoiceStmt->bindValue(':invoice_id', $invoice_id);
    $invoiceStmt->bindValue(':user_id', $user_id);
    $invoiceStmt->execute();

    if ($invoiceStmt->rowCount() == 0) {
      throw new Exception("Invoice not found or does not belong to you");
    }

    $invoice = $invoiceStmt->fetch(PDO::FETCH_ASSOC);

    // Update the invoice with the credit amount
    $updateSql = "UPDATE invoices
                  SET value = GREATEST(0, value - :credit_amount),
                      credit = COALESCE(credit, 0) + :credit_amount,
                      credit_applied = 1
                  WHERE id = :invoice_id";
    $updateStmt = $pdo->prepare($updateSql);
    $updateStmt->bindValue(':credit_amount', $credit_amount);
    $updateStmt->bindValue(':invoice_id', $invoice_id);
    $updateStmt->execute();

    // Get the updated invoice
    $updatedSql = "SELECT * FROM invoices WHERE id = :invoice_id";
    $updatedStmt = $pdo->prepare($updatedSql);
    $updatedStmt->bindValue(':invoice_id', $invoice_id);
    $updatedStmt->execute();
    $updatedInvoice = $updatedStmt->fetch(PDO::FETCH_ASSOC);

    // If invoice value is now 0, mark it as paid
    if (floatval($updatedInvoice['value']) <= 0) {
      $paidSql = "UPDATE invoices
                  SET paid = 1,
                      paid_date = NOW()
                  WHERE id = :invoice_id";
      $paidStmt = $pdo->prepare($paidSql);
      $paidStmt->bindValue(':invoice_id', $invoice_id);
      $paidStmt->execute();

      // Get the final invoice
      $finalSql = "SELECT * FROM invoices WHERE id = :invoice_id";
      $finalStmt = $pdo->prepare($finalSql);
      $finalStmt->bindValue(':invoice_id', $invoice_id);
      $finalStmt->execute();
      $updatedInvoice = $finalStmt->fetch(PDO::FETCH_ASSOC);

      // Send paid invoice email notification
      try {
        // Check if invoice_email_functions.php exists and include it
        if (file_exists('invoice_email_functions.php')) {
          require_once('invoice_email_functions.php');

          // Check if the sendInvoicePaidEmail function exists
          if (function_exists('sendInvoicePaidEmail')) {
            error_log("Sending paid invoice email notification for invoice #$invoice_id");
            $emailResult = sendInvoicePaidEmail($invoice_id);

            if ($emailResult) {
              error_log("Successfully sent paid invoice email notification for invoice #$invoice_id");
            } else {
              error_log("Failed to send paid invoice email notification for invoice #$invoice_id");
            }
          } else {
            error_log("sendInvoicePaidEmail function not found");
          }
        } else {
          error_log("invoice_email_functions.php file not found");
        }
      } catch (Exception $emailError) {
        error_log("Error sending paid invoice email notification: " . $emailError->getMessage());
        // Don't throw the exception - we don't want to fail the invoice payment if email fails
      }
    }

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Invoice updated with credit',
      'invoice' => $updatedInvoice
    ]);

  } catch (Exception $e) {
    // Return error response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

elseif($_GET['f'] == 'record_payment'){
  try {
    // Get token and authenticate user
    $data = json_decode(file_get_contents('php://input'), true);
    $user_id = auth_user();

    // Validate required parameters
    if (!isset($data['invoice_id']) || empty($data['invoice_id'])) {
      throw new Exception("Invoice ID is required");
    }

    if (!isset($data['amount']) || !is_numeric($data['amount']) || $data['amount'] <= 0) {
      throw new Exception("Amount is required and must be positive");
    }

    if (!isset($data['payment_method']) || empty($data['payment_method'])) {
      throw new Exception("Payment method is required");
    }

    $invoice_id = $data['invoice_id'];
    $amount = floatval($data['amount']);
    $payment_method = $data['payment_method'];
    $description = isset($data['description']) ? $data['description'] : "Payment via $payment_method";

    // Generate a transaction ID
    $transaction_id = 'TXN' . time() . rand(1000, 9999);

    // Create transaction record using direct SQL
    $sql = "INSERT INTO invoice_transactions
            (id, invoice_id, user_id, date, type, amount, description, status, processor)
            VALUES
            ('$transaction_id', '$invoice_id', $user_id, NOW(), 'Payment', $amount, '$description', 'Completed', '$payment_method')";

    $pdo->exec($sql);

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Payment recorded successfully',
      'transaction_id' => $transaction_id
    ]);

  } catch (Exception $e) {
    // Return error response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// Add these endpoints to your api.php file

elseif($_GET['f'] == 'record_direct_payment') {
  try {
    // Get request data
    $data = json_decode(file_get_contents('php://input'), true);
    $user_id = auth_user();

    // Log the request for debugging
    error_log("Recording direct payment: " . json_encode($data));

    // Validate required fields
    if (!isset($data['invoice_id']) || empty($data['invoice_id'])) {
      throw new Exception("Invoice ID is required");
    }

    if (!isset($data['amount']) || !is_numeric($data['amount']) || $data['amount'] <= 0) {
      throw new Exception("Valid amount is required");
    }

    // Extract parameters
    $invoice_id = $data['invoice_id'];
    $amount = floatval($data['amount']);
    $payment_method = isset($data['payment_method']) ? $data['payment_method'] : 'account_credit';
    $description = isset($data['description']) ? $data['description'] : 'Account credit applied to invoice';

    // Verify invoice exists and belongs to user
    $sth = $pdo->prepare("SELECT * FROM invoices WHERE id = :invoice_id AND user_id = :user_id");
    $sth->bindValue(':invoice_id', $invoice_id);
    $sth->bindValue(':user_id', $user_id);
    $sth->execute();

    if ($sth->rowCount() == 0) {
      throw new Exception("Invoice not found or doesn't belong to this user");
    }

    $invoice = $sth->fetch(PDO::FETCH_ASSOC);

    // Begin transaction
    $pdo->beginTransaction();

    // 1. Insert transaction record
    $txn_id = 'TXN' . time() . rand(1000, 9999);

    // Use direct SQL execution to avoid parameter binding issues
    $sql = "INSERT INTO invoice_transactions
            (id, invoice_id, user_id, date, type, amount, description, status, processor)
            VALUES
            ('$txn_id', '$invoice_id', $user_id, NOW(), 'Payment', $amount, '$description', 'Completed', '$payment_method')";

    $pdo->exec($sql);

    // 2. Update the invoice - mark it as paid and set paid_date
    $updateSql = "UPDATE invoices
                  SET paid = 1,
                      paid_date = NOW(),
                      credit_applied = 1,
                      credit = $amount
                  WHERE id = '$invoice_id'";

    $pdo->exec($updateSql);

    // Send paid invoice email notification
    try {
      // Check if invoice_email_functions.php exists and include it
      if (file_exists('invoice_email_functions.php')) {
        require_once('invoice_email_functions.php');

        // Check if the sendInvoicePaidEmail function exists
        if (function_exists('sendInvoicePaidEmail')) {
          error_log("Sending paid invoice email notification for invoice #$invoice_id");
          $emailResult = sendInvoicePaidEmail($invoice_id);

          if ($emailResult) {
            error_log("Successfully sent paid invoice email notification for invoice #$invoice_id");
          } else {
            error_log("Failed to send paid invoice email notification for invoice #$invoice_id");
          }
        } else {
          error_log("sendInvoicePaidEmail function not found");
        }
      } else {
        error_log("invoice_email_functions.php file not found");
      }
    } catch (Exception $emailError) {
      error_log("Error sending paid invoice email notification: " . $emailError->getMessage());
      // Don't throw the exception - we don't want to fail the invoice payment if email fails
    }

    // 3. Update the user's credit balance
    if ($payment_method == 'account_credit') {
      // Get current credit balance
      $creditQuery = "SELECT
      SUM(CASE WHEN type = 'regular' OR (type = 'used' AND status = 'applied') THEN amount ELSE 0 END) as regular_credit,
      SUM(CASE WHEN type = 'free' THEN amount ELSE 0 END) as free_credit
     FROM user_credits
     WHERE user_id = ?";

      $creditResult = $pdo->query($creditQuery);
      $creditInfo = $creditResult->fetch(PDO::FETCH_ASSOC);

      $regular_credit = floatval($creditInfo['regular_credit'] ?? 0);
      $free_credit = floatval($creditInfo['free_credit'] ?? 0);
      $total_credit = $regular_credit + $free_credit;

      if ($total_credit < $amount) {
        // Not enough credit, but we'll still mark the invoice as paid
        error_log("Warning: User $user_id doesn't have enough credit: has $total_credit, needs $amount");
      } else {
        // Deduct from free credit first, then regular
        $remaining = $amount;

        // Deduct from free credit if available
        if ($free_credit > 0 && $remaining > 0) {
          $deduct_amount = min($free_credit, $remaining);

          // Find free credit records to update
          $freeQuery = "SELECT id, amount FROM user_credits
                        WHERE user_id = $user_id
                        AND type = 'free'
                        AND status = 'available'
                        AND amount > 0
                        ORDER BY created ASC";

          $freeResult = $pdo->query($freeQuery);
          while ($record = $freeResult->fetch(PDO::FETCH_ASSOC) and $deduct_amount > 0) {
            $record_id = $record['id'];
            $record_amount = floatval($record['amount']);

            if ($record_amount <= $deduct_amount) {
              // Use entire record
              $updateQuery = "UPDATE user_credits SET amount = 0, status = 'used' WHERE id = $record_id";
              $pdo->exec($updateQuery);
              $deduct_amount -= $record_amount;
            } else {
              // Use partial record
              $updateQuery = "UPDATE user_credits SET amount = amount - $deduct_amount WHERE id = $record_id";
              $pdo->exec($updateQuery);
              $deduct_amount = 0;
            }
          }

          $remaining -= ($free_credit - $deduct_amount);
        }

        // Deduct from regular credit if needed
        if ($remaining > 0) {
          // Find regular credit records to update
          $regularQuery = "SELECT id, amount FROM user_credits
                          WHERE user_id = $user_id
                          AND type = 'regular'
                          AND status = 'available'
                          AND amount > 0
                          ORDER BY created ASC";

          $regularResult = $pdo->query($regularQuery);
          while ($record = $regularResult->fetch(PDO::FETCH_ASSOC) and $remaining > 0) {
            $record_id = $record['id'];
            $record_amount = floatval($record['amount']);

            if ($record_amount <= $remaining) {
              // Use entire record
              $updateQuery = "UPDATE user_credits SET amount = 0, status = 'used' WHERE id = $record_id";
              $pdo->exec($updateQuery);
              $remaining -= $record_amount;
            } else {
              // Use partial record
              $updateQuery = "UPDATE user_credits SET amount = amount - $remaining WHERE id = $record_id";
              $pdo->exec($updateQuery);
              $remaining = 0;
            }
          }
        }

        // Clean up zero-amount credits
        $cleanupQuery = "DELETE FROM user_credits WHERE user_id = $user_id AND amount = 0";
        $pdo->exec($cleanupQuery);
      }
    }

    // Commit transaction
    $pdo->commit();

    // Success response
    $response = [
      'success' => true,
      'message' => 'Payment recorded and invoice marked as paid',
      'transaction_id' => $txn_id
    ];

    // Log success and return response
    error_log("Payment recorded successfully: $txn_id");
    header('Content-Type: application/json');
    echo json_encode($response);

  } catch (Exception $e) {
    // Rollback if transaction is active
    if ($pdo->inTransaction()) {
      $pdo->rollBack();
    }

    // Log error and return error response
    error_log("Error recording payment: " . $e->getMessage());
    header('Content-Type: application/json');
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

elseif($_GET['f'] == 'force_paid_invoice') {
  try {
    // Get request data
    $data = json_decode(file_get_contents('php://input'), true);
    $user_id = auth_user();

    // Log the request for debugging
    error_log("Force marking invoice as paid: " . json_encode($data));

    // Validate required fields
    if (!isset($data['invoice_id']) || empty($data['invoice_id'])) {
      throw new Exception("Invoice ID is required");
    }

    $invoice_id = $data['invoice_id'];
    $credit_amount = isset($data['credit_amount']) ? floatval($data['credit_amount']) : 0;

    // Verify invoice exists and belongs to user - no transaction needed for read-only
    $sth = $pdo->prepare("
      SELECT i.*, i.order_id as related_order_id
      FROM invoices i
      WHERE i.id = :invoice_id AND i.user_id = :user_id
    ");
    $sth->bindValue(':invoice_id', $invoice_id);
    $sth->bindValue(':user_id', $user_id);
    $sth->execute();

    if ($sth->rowCount() == 0) {
      throw new Exception("Invoice not found or doesn't belong to this user");
    }

    $invoice = $sth->fetch(PDO::FETCH_ASSOC);
    $order_id = $invoice['related_order_id'];

    // Begin transaction
    $pdo->beginTransaction();
    error_log("Started transaction for invoice payment");

    try {
      // Direct SQL updates to fix the invoice
      $updateInvoiceSql = "UPDATE invoices
                          SET paid = 1,
                              paid_date = NOW()";

      // Add credit information if applicable
      if ($credit_amount > 0) {
        $updateInvoiceSql .= ", credit_applied = 1,
                              credit = " . floatval($credit_amount);
      }

      $updateInvoiceSql .= " WHERE id = '$invoice_id'";

      error_log("Update invoice SQL: $updateInvoiceSql");
      $pdo->exec($updateInvoiceSql);

      // Commit the invoice update transaction
      $pdo->commit();
      error_log("Committed invoice update transaction");

      // Send paid invoice email notification
      try {
        // Check if invoice_email_functions.php exists and include it
        if (file_exists('invoice_email_functions.php')) {
          require_once('invoice_email_functions.php');

          // Check if the sendInvoicePaidEmail function exists
          if (function_exists('sendInvoicePaidEmail')) {
            error_log("Sending paid invoice email notification for invoice #$invoice_id");
            $emailResult = sendInvoicePaidEmail($invoice_id);

            if ($emailResult) {
              error_log("Successfully sent paid invoice email notification for invoice #$invoice_id");
            } else {
              error_log("Failed to send paid invoice email notification for invoice #$invoice_id");
            }
          } else {
            error_log("sendInvoicePaidEmail function not found");
          }
        } else {
          error_log("invoice_email_functions.php file not found");
        }
      } catch (Exception $emailError) {
        error_log("Error sending paid invoice email notification: " . $emailError->getMessage());
        // Don't throw the exception - we don't want to fail the invoice payment if email fails
      }

      // Server allocation - outside the invoice transaction
      $server_allocated = false;
      $server_id = 0;

      // If this invoice is for an order, allocate a server
      if ($order_id) {
        error_log("Invoice $invoice_id is for order $order_id - attempting server allocation");
        list($server_allocated, $server_id) = allocate_server_for_order($pdo, $order_id);
        error_log("Server allocation result: " . ($server_allocated ? "Success" : "Not allocated") .
                  ", Server ID: $server_id");
      }

      // Success response
      $response = [
        'success' => true,
        'message' => 'Invoice marked as paid successfully',
        'server_allocated' => $server_allocated,
        'server_id' => $server_id
      ];

      // Log success and return response
      error_log("Invoice marked as paid successfully: $invoice_id");
      header('Content-Type: application/json');
      echo json_encode($response);
    } catch (Exception $e) {
      // Rollback if there's an active transaction
      if ($pdo->inTransaction()) {
        $pdo->rollBack();
        error_log("Rolled back invoice update transaction due to error");
      }
      throw $e;
    }
  } catch (Exception $e) {
    // Log error and return error response
    error_log("Error marking invoice as paid: " . $e->getMessage());
    header('Content-Type: application/json');
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}


elseif($_GET['f'] == 'apply_invoice_credit') {
  try {
    // Enable detailed error logging
    error_log("=== APPLY_INVOICE_CREDIT CALLED ===");

    // Get request data
    $data = json_decode(file_get_contents('php://input'), true);
    $user_id = auth_user();

    // Rest of the validation and credit application logic...
    // [original validation code]

    $invoice_id = $data['invoice_id'];
    $credit_amount = floatval($data['amount']);

    // Begin transaction
    $pdo->beginTransaction();

    // [original credit application logic]

    // Check if this fully pays the invoice
    $finalInvoiceQuery = "SELECT * FROM invoices WHERE id = ?";
    $finalInvoiceStmt = $pdo->prepare($finalInvoiceQuery);
    $finalInvoiceStmt->execute([$invoice_id]);
    $finalInvoice = $finalInvoiceStmt->fetch(PDO::FETCH_ASSOC);

    $fully_paid = floatval($finalInvoice['value']) <= 0;

    // If invoice is now fully paid, mark it as paid and allocate server if needed
    if ($fully_paid) {
      error_log("Invoice is now fully paid - updating paid status");

      // Mark invoice as paid
      $markPaidQuery = "UPDATE invoices SET paid = 1, paid_date = NOW() WHERE id = ?";
      $markPaidStmt = $pdo->prepare($markPaidQuery);
      $markPaidStmt->execute([$invoice_id]);

      // Send paid invoice email notification
      try {
        // Check if invoice_email_functions.php exists and include it
        if (file_exists('invoice_email_functions.php')) {
          require_once('invoice_email_functions.php');

          // Check if the sendInvoicePaidEmail function exists
          if (function_exists('sendInvoicePaidEmail')) {
            error_log("Sending paid invoice email notification for invoice #$invoice_id");
            $emailResult = sendInvoicePaidEmail($invoice_id);

            if ($emailResult) {
              error_log("Successfully sent paid invoice email notification for invoice #$invoice_id");
            } else {
              error_log("Failed to send paid invoice email notification for invoice #$invoice_id");
            }
          } else {
            error_log("sendInvoicePaidEmail function not found");
          }
        } else {
          error_log("invoice_email_functions.php file not found");
        }
      } catch (Exception $emailError) {
        error_log("Error sending paid invoice email notification: " . $emailError->getMessage());
        // Don't throw the exception - we don't want to fail the invoice payment if email fails
      }

      // If this invoice is for an order, try to allocate a server
      $order_id = $finalInvoice['order_id'];
      if ($order_id) {
        error_log("Invoice $invoice_id is for order $order_id - attempting server allocation");
        list($server_allocated, $server_id) = allocate_server_for_order($pdo, $order_id);
        error_log("Server allocation result: " . ($server_allocated ? "Success" : "Not allocated") .
                  ", Server ID: $server_id");
      }
    }

    // Commit the transaction
    $pdo->commit();

    // Success response
    $response = [
      'success' => true,
      'message' => "Successfully applied €$credit_amount credit to invoice",
      'invoice' => $finalInvoice,
      'fully_paid' => $fully_paid,
      'server_allocated' => $server_allocated ?? false,
      'server_id' => $server_id ?? 0
    ];

    error_log("Credit application successful");
    header('Content-Type: application/json');
    echo json_encode($response);

  } catch (Exception $e) {
    // Rollback on error
    if ($pdo->inTransaction()) {
      $pdo->rollBack();
    }

    error_log("Credit application failed: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());

    // Error response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}



// Add this endpoint to your api.php file
elseif($_GET['f'] == 'direct_credit_application'){
  try {
    error_log("=== DIRECT CREDIT APPLICATION CALLED ===");

    // Authenticate user
    $user_id = auth_user();
    error_log("User authenticated: user_id = $user_id");

    // Get request data
    $data = json_decode(file_get_contents('php://input'), true);
    error_log("Request data: " . json_encode($data));

    // Validate required parameters
    if (!isset($data['invoice_id']) || !isset($data['amount'])) {
      throw new Exception("Missing required parameters: invoice_id and amount");
    }

    $invoice_id = $data['invoice_id'];
    $amount = floatval($data['amount']);

    if ($amount <= 0) {
      throw new Exception("Invalid amount. Must be greater than zero.");
    }

    // Get the invoice - no transaction needed for read-only
    $sth = $pdo->prepare("
      SELECT * FROM invoices
      WHERE id = :invoice_id AND user_id = :user_id
    ");
    $sth->bindValue(':invoice_id', $invoice_id);
    $sth->bindValue(':user_id', $user_id);
    $sth->execute();

    if ($sth->rowCount() == 0) {
      throw new Exception("Invoice not found or doesn't belong to this user");
    }

    $invoice = $sth->fetch(PDO::FETCH_ASSOC);
    $order_id = $invoice['order_id'];

    // Check if invoice is already paid
    if ($invoice['paid'] == 1) {
      throw new Exception("Invoice is already fully paid");
    }

    // Get user's available credit - no transaction needed for read-only
    $creditSth = $pdo->prepare("
      SELECT
        SUM(CASE WHEN type = 'regular' AND status = 'available' THEN amount ELSE 0 END) as available_credit,
        SUM(CASE WHEN type = 'free' AND status = 'available' THEN amount ELSE 0 END) as free_credit
      FROM user_credits
      WHERE user_id = :user_id
    ");
    $creditSth->bindValue(':user_id', $user_id);
    $creditSth->execute();
    $credit = $creditSth->fetch(PDO::FETCH_ASSOC);

    $total_credit = floatval($credit['available_credit'] ?? 0) + floatval($credit['free_credit'] ?? 0);

    if ($total_credit < $amount) {
      throw new Exception("Insufficient credit. Available: €$total_credit, Requested: €$amount");
    }

    // Begin transaction for credit application
    $pdo->beginTransaction();
    error_log("Started transaction for credit application");

    try {
      // Apply credit to invoice logic...
      // [original credit application code here]

      // Check if invoice is now fully paid
      $sth = $pdo->prepare("SELECT * FROM invoices WHERE id = :invoice_id");
      $sth->bindValue(':invoice_id', $invoice_id);
      $sth->execute();
      $updated_invoice = $sth->fetch(PDO::FETCH_ASSOC);

      // If the remaining amount is now 0, mark as paid
      $is_fully_paid = (floatval($updated_invoice['value']) <= 0);

      if ($is_fully_paid) {
        $sth = $pdo->prepare("
          UPDATE invoices
          SET paid = 1,
              paid_date = NOW()
          WHERE id = :invoice_id
        ");
        $sth->bindValue(':invoice_id', $invoice_id);
        $sth->execute();

        error_log("Invoice $invoice_id marked as fully paid");

        // Send paid invoice email notification
        try {
          // Check if invoice_email_functions.php exists and include it
          if (file_exists('invoice_email_functions.php')) {
            require_once('invoice_email_functions.php');

            // Check if the sendInvoicePaidEmail function exists
            if (function_exists('sendInvoicePaidEmail')) {
              error_log("Sending paid invoice email notification for invoice #$invoice_id");
              $emailResult = sendInvoicePaidEmail($invoice_id);

              if ($emailResult) {
                error_log("Successfully sent paid invoice email notification for invoice #$invoice_id");
              } else {
                error_log("Failed to send paid invoice email notification for invoice #$invoice_id");
              }
            } else {
              error_log("sendInvoicePaidEmail function not found");
            }
          } else {
            error_log("invoice_email_functions.php file not found");
          }
        } catch (Exception $emailError) {
          error_log("Error sending paid invoice email notification: " . $emailError->getMessage());
          // Don't throw the exception - we don't want to fail the invoice payment if email fails
        }
      }

      // Commit the credit application transaction
      $pdo->commit();
      error_log("Committed credit application transaction");

      // Server allocation - outside the credit transaction
      $server_allocated = false;
      $server_id = 0;

      // Only try to allocate server if invoice is fully paid and has an order
      if ($is_fully_paid && $order_id) {
        error_log("Invoice $invoice_id is fully paid for order $order_id - attempting server allocation");
        list($server_allocated, $server_id) = allocate_server_for_order($pdo, $order_id);
        error_log("Server allocation result: " . ($server_allocated ? "Success" : "Not allocated") .
                  ", Server ID: $server_id");
      }

      // Success response
      echo json_encode([
        'success' => true,
        'message' => 'Credit applied successfully',
        'amount_applied' => $amount,
        'fully_paid' => $is_fully_paid,
        'server_allocated' => $server_allocated,
        'server_id' => $server_id
      ]);
    } catch (Exception $e) {
      // Rollback if there's an active transaction
      if ($pdo->inTransaction()) {
        $pdo->rollBack();
        error_log("Rolled back credit application transaction due to error");
      }
      throw $e;
    }
  } catch (Exception $e) {
    error_log("Error in direct_credit_application: " . $e->getMessage());
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }

  exit;
}

elseif($_GET['f'] == 'get_vat_rate') {
  try {
    // Get user information
    $user_id = auth_user();

    // Get user's country and VAT ID from database
    $userSth = $pdo->prepare("SELECT country, vat_id FROM users WHERE id = :user_id");
    $userSth->bindValue(':user_id', $user_id);
    $userSth->execute();
    $user = $userSth->fetch(PDO::FETCH_ASSOC);

    if (!$user) {
      throw new Exception("User not found");
    }

    $country = $user['country'];
    $vat_id = $user['vat_id'];

    // Default VAT rate is 0%
    $vatRate = 0;
    $isVatExempt = false;

    // Check if user has a valid VAT ID (B2B EU transactions might be exempt)
    if ($vat_id && strlen(trim($vat_id)) > 3) {
      // For EU B2B with valid VAT ID, set as exempt (0% rate)
      $isVatExempt = true;
    } else if ($country) {
      // Get VAT rate from database based on country
      $vatSth = $pdo->prepare("SELECT rate FROM vat_rates WHERE country = :country");
      $vatSth->bindValue(':country', $country);
      $vatSth->execute();
      $vat = $vatSth->fetch(PDO::FETCH_ASSOC);

      if ($vat) {
        $vatRate = floatval($vat['rate']);
      } else {
        // If country not found in VAT rates table, use default rate of 0%
        $vatRate = 0;
      }
    }

    // Return VAT information
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'country' => $country,
      'vat_rate' => $vatRate,
      'vat_exempt' => $isVatExempt,
      'vat_id' => $vat_id
    ]);

  } catch (Exception $e) {
    header('Content-Type: application/json');
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}




elseif($_GET['f'] == 'create_ticket'){
  // Debug: Log the request
  error_log("create_ticket called with data: " . file_get_contents('php://input'));

  try {
      // Get data first for token extraction
      $data = json_decode(file_get_contents('php://input'), true);
      if (!$data) {
          error_log("Failed to decode JSON input: " . json_last_error_msg());
          throw new Exception("Invalid JSON input");
      }

      // Authenticate user (function updated to handle token in request body)
      $user_id = auth_user();
      error_log("User authenticated: user_id = $user_id");

      // Validate required fields
      if(!isset($data['subject']) || trim($data['subject']) == '' ||
         !isset($data['message']) || trim($data['message']) == '') {
          $reply['error'] = 3; // Missing required fields
          $reply['message'] = "Missing required fields";
          die(json_encode($reply));
      }

      // Extract and sanitize fields
      $subject = $data['subject'];
      $department = isset($data['department']) ? $data['department'] : 'Technical (NOC) 24/7';
      $priority = isset($data['priority']) ? $data['priority'] : 'Low';
      $message = $data['message'];

      error_log("Creating ticket with: subject=$subject, department=$department, priority=$priority");

      // Check if tickets table exists
      $tableExists = $pdo->query("SHOW TABLES LIKE 'tickets'");
      if($tableExists->rowCount() == 0) {
          // Create tickets table if it doesn't exist - without related_service_id column
          $createTable = "CREATE TABLE IF NOT EXISTS `tickets` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) NOT NULL,
              `subject` varchar(255) NOT NULL,
              `department` varchar(50) NOT NULL DEFAULT '24/7 NOC',
              `status` varchar(20) NOT NULL DEFAULT 'Open',
              `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
              `last_reply` timestamp NULL DEFAULT NULL,
              `last_reply_by` enum('user','staff') DEFAULT NULL,
              `priority` varchar(20) DEFAULT 'Low',
              PRIMARY KEY (`id`),
              KEY `user_id` (`user_id`)
          ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

          $pdo->exec($createTable);
          error_log("Created tickets table");

          // Create ticket_messages table if it doesn't exist
          $createMessagesTable = "CREATE TABLE IF NOT EXISTS `ticket_messages` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `ticket_id` int(11) NOT NULL,
              `user_id` int(11) NOT NULL,
              `type` enum('customer','zet') NOT NULL,
              `message` text NOT NULL,
              `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
              PRIMARY KEY (`id`),
              KEY `ticket_id` (`ticket_id`)
          ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

          $pdo->exec($createMessagesTable);
          error_log("Created ticket_messages table");
      }

      // Get the actual table structure to check available columns
      $columnsResult = $pdo->query("SHOW COLUMNS FROM tickets");
      $columns = [];
      while ($row = $columnsResult->fetch(PDO::FETCH_ASSOC)) {
          $columns[] = $row['Field'];
      }
      error_log("Available columns in tickets table: " . implode(', ', $columns));

      // Start transaction
      $pdo->beginTransaction();
      error_log("Transaction started");

      // Check if related_service_id column exists
      $hasRelatedServiceColumn = in_array('related_service_id', $columns);

      // Insert ticket - use correct columns based on table structure
      if ($hasRelatedServiceColumn) {
          // Table has related_service_id column
          $related_service = isset($data['related_service']) ? $data['related_service'] : null;

          $sql = "INSERT INTO tickets
                  (user_id, subject, department, status, related_service_id, priority, created_at, last_reply)
                  VALUES
                  ($user_id, " . $pdo->quote($subject) . ", " . $pdo->quote($department) . ", 'Open', ";

          if ($related_service) {
              $sql .= $pdo->quote($related_service) . ", ";
          } else {
              $sql .= "NULL, ";
          }

          $sql .= $pdo->quote($priority) . ", NOW(), NOW())";
      } else {
          // Table doesn't have related_service_id column
          $sql = "INSERT INTO tickets
                  (user_id, subject, department, status, priority, created_at, last_reply)
                  VALUES
                  ($user_id, " . $pdo->quote($subject) . ", " . $pdo->quote($department) . ", 'Open', " .
                  $pdo->quote($priority) . ", NOW(), NOW())";
      }

      error_log("Ticket SQL: $sql");
      $pdo->exec($sql);

      $ticket_id = $pdo->lastInsertId();
      error_log("Created ticket with ID: $ticket_id");

      // Insert first message - use direct SQL to avoid binding issues
      $messageSql = "INSERT INTO ticket_messages
                    (ticket_id, user_id, type, message, created_at)
                    VALUES
                    ($ticket_id, $user_id, 'customer', " . $pdo->quote($message) . ", NOW())";

      error_log("Message SQL: $messageSql");
      $pdo->exec($messageSql);

      // Commit transaction
      $pdo->commit();
      error_log("Transaction committed");

      // Success response
      $reply['success'] = true;
      $reply['ticket_id'] = $ticket_id;
      error_log("Returning success response: " . json_encode($reply));
      die(json_encode($reply));

      $sql = "INSERT INTO activity_log
      (user_id, action, description, user_name, timestamp)
      VALUES
      (:user_id, :action, :description, :user_name, NOW())";

      $stmt = $pdo->prepare($sql);
      $stmt->bindValue(':user_id', $user_id);
      $stmt->bindValue(':action', 'New Task Created');
      $stmt->bindValue(':description', $subject);

      // Get user name
      $user_sth = $pdo->prepare("SELECT first_name, last_name FROM users WHERE id = :user_id");
      $user_sth->bindValue(':user_id', $user_id);
      $user_sth->execute();
      $user = $user_sth->fetch(PDO::FETCH_ASSOC);
      $user_name = ($user['first_name'] ?? '') . ' ' . ($user['last_name'] ?? '');

      $stmt->bindValue(':user_name', $user_name);
      $stmt->execute();


  } catch (Exception $e) {
      // Rollback transaction if an error occurred
      if ($pdo->inTransaction()) {
          $pdo->rollBack();
          error_log("Transaction rolled back");
      }

      error_log("Error creating ticket: " . $e->getMessage());
      error_log("Stack trace: " . $e->getTraceAsString());

      $reply['error'] = 6; // Database error
      $reply['message'] = "Failed to create ticket: " . $e->getMessage();
      error_log("Returning error response: " . json_encode($reply));
      die(json_encode($reply));
  }
}


// ADD MESSAGE TO TICKET WITH FILE SUPPORT
elseif($_GET['f'] == 'add_message'){
  error_log("add_message called");

  try {
      // Authenticate user
      $user_id = auth_user();
      error_log("User authenticated: user_id = $user_id");

      // Check where the data is coming from - either JSON or form data
      $is_multipart = false;
      $has_file = false;

      // Handle both JSON and multipart data
      if (isset($_POST['ticket_id'])) {
          // Form data with possible file
          $is_multipart = true;
          $ticket_id = $_POST['ticket_id'];
          $message = $_POST['message'] ?? '';
          error_log("Handling as multipart form data");
      } else {
          // JSON data
          $data = json_decode(file_get_contents('php://input'), true);
          if (!$data) {
              error_log("Failed to decode JSON input: " . json_last_error_msg());
              throw new Exception("Invalid input data");
          }
          $ticket_id = $data['ticket_id'] ?? null;
          $message = $data['message'] ?? '';
          error_log("Handling as JSON data");
      }

      // Validate ticket ID
      if(!$ticket_id) {
          $reply = ['error' => 3, 'message' => "Missing ticket ID"];
          die(json_encode($reply));
      }

      // Check if ticket exists and belongs to the user
      $sth = $pdo->prepare("SELECT * FROM tickets WHERE id = :ticket_id AND user_id = :user_id");
      $sth->bindValue(':ticket_id', $ticket_id);
      $sth->bindValue(':user_id', $user_id);
      $sth->execute();

      if($sth->rowCount() != 1){
          $reply = ['error' => 4, 'message' => "Ticket not found or doesn't belong to you"];
          die(json_encode($reply));
      }

      // Check for file upload
      $file_path = null;
      $file_name = null;
      $file_url = null;

      if ($is_multipart && isset($_FILES['file']) && $_FILES['file']['error'] == 0) {
          error_log("File upload detected");
          $has_file = true;

          // Get file details
          $tmp_file = $_FILES['file']['tmp_name'];
          $file_name = $_FILES['file']['name'];
          $file_size = $_FILES['file']['size'];
          $file_type = $_FILES['file']['type'];

          // Validate file size (5MB limit)
          if ($file_size > 5 * 1024 * 1024) {
              $reply = ['error' => 5, 'message' => "File size exceeds the 5MB limit"];
              die(json_encode($reply));
          }

          // Create uploads directory if it doesn't exist
          $upload_dir = __DIR__ . '/uploads/tickets/' . $ticket_id;
          if (!file_exists($upload_dir)) {
              mkdir($upload_dir, 0755, true);
          }

          // Generate a unique filename
          $unique_name = time() . '_' . preg_replace('/[^a-zA-Z0-9_.-]/', '', $file_name);
          $file_path = $upload_dir . '/' . $unique_name;

          // Move the uploaded file
          if (!move_uploaded_file($tmp_file, $file_path)) {
              error_log("Failed to move uploaded file from $tmp_file to $file_path");
              throw new Exception("Failed to save uploaded file");
          }

          // Generate the URL for the file
          $file_url = '/uploads/tickets/' . $ticket_id . '/' . $unique_name;
          error_log("File saved to: $file_path, URL: $file_url");
      }

      // Require at least a message or a file
      if (!$message && !$has_file) {
          $reply = ['error' => 3, 'message' => "A message or file attachment is required"];
          die(json_encode($reply));
      }

      // Start transaction
      $pdo->beginTransaction();

      // Create ticket_attachments table if it doesn't exist
      $pdo->exec("CREATE TABLE IF NOT EXISTS `ticket_attachments` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `message_id` int(11) NOT NULL,
          `file_name` varchar(255) NOT NULL,
          `file_path` varchar(255) NOT NULL,
          `file_url` varchar(255) NOT NULL,
          `file_type` varchar(100) DEFAULT NULL,
          `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          KEY `message_id` (`message_id`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");

      // Insert message
      $message_sql = "INSERT INTO ticket_messages
                     (ticket_id, user_id, type, message, created_at)
                     VALUES
                     (:ticket_id, :user_id, 'customer', :message, NOW())";

      $sth = $pdo->prepare($message_sql);
      $sth->bindValue(':ticket_id', $ticket_id);
      $sth->bindValue(':user_id', $user_id);
      $sth->bindValue(':message', $message);
      $sth->execute();

      $message_id = $pdo->lastInsertId();
      error_log("Message inserted with ID: $message_id");

      // Insert file attachment if present
      if ($has_file && $file_path && $file_url) {
          $attach_sql = "INSERT INTO ticket_attachments
                        (message_id, file_name, file_path, file_url, file_type)
                        VALUES
                        (:message_id, :file_name, :file_path, :file_url, :file_type)";

          $sth = $pdo->prepare($attach_sql);
          $sth->bindValue(':message_id', $message_id);
          $sth->bindValue(':file_name', $file_name);
          $sth->bindValue(':file_path', $file_path);
          $sth->bindValue(':file_url', $file_url);
          $sth->bindValue(':file_type', $file_type ?? null);
          $sth->execute();

          error_log("Attachment inserted for message $message_id");
      }

      // Update ticket's last_reply and last_reply_by
      $update_sql = "UPDATE tickets
                   SET last_reply = NOW(),
                       last_reply_by = 'user'
                   WHERE id = :ticket_id";

      $sth = $pdo->prepare($update_sql);
      $sth->bindValue(':ticket_id', $ticket_id);
      $sth->execute();

      // If ticket is closed, reopen it
      $reopen_sql = "UPDATE tickets
                    SET status = 'Open'
                    WHERE id = :ticket_id AND status = 'Closed'";

      $sth = $pdo->prepare($reopen_sql);
      $sth->bindValue(':ticket_id', $ticket_id);
      $sth->execute();

      // Commit transaction
      $pdo->commit();

      // Success response
      $reply = [
          'success' => true,
          'message' => "Message added successfully",
          'message_id' => $message_id
      ];

      // Include attachment URL in response if a file was uploaded
      if ($has_file && $file_url) {
          $reply['attachment_url'] = $file_url;
      }

      die(json_encode($reply));

  } catch (Exception $e) {
      // Rollback transaction if an error occurred
      if ($pdo->inTransaction()) {
          $pdo->rollBack();
      }
      error_log("Error adding message: " . $e->getMessage());

      $reply = [
          'error' => 6,
          'message' => "Failed to add message: " . $e->getMessage()
      ];

      die(json_encode($reply));
  }
}

// GET TICKET DETAILS
elseif($_GET['f'] == 'ticket_details'){
  $user_id = auth_user();

  if(!isset($_GET['id'])) {
      $reply['error'] = 3; // Missing ticket ID
      die(json_encode($reply));
  }

  $ticket_id = $_GET['id'];

  try {
      // Get ticket details
      $sth = $pdo->prepare("SELECT t.*,
                           DATE_FORMAT(t.created_at, '%d.%m.%Y %H:%i') as created_date,
                           DATE_FORMAT(t.last_reply, '%d.%m.%Y %H:%i') as last_reply_date
                           FROM tickets t
                           WHERE t.id = :ticket_id AND t.user_id = :user_id");

      $sth->bindValue(':ticket_id', $ticket_id);
      $sth->bindValue(':user_id', $user_id);
      $sth->execute();

      if($sth->rowCount() != 1){
          $reply['error'] = 4; // Ticket not found or doesn't belong to user
          die(json_encode($reply));
      }

      $ticket = $sth->fetch(PDO::FETCH_ASSOC);

      // Success response
      $reply = $ticket;
      die(json_encode($reply));

  } catch (Exception $e) {
      error_log("Error getting ticket details: " . $e->getMessage());

      $reply['error'] = 6; // Database error
      $reply['message'] = "Failed to get ticket details: " . $e->getMessage();
      die(json_encode($reply));
  }
}

// GET TICKET MESSAGES - IMPROVED VERSION FOR TICKET.JS WITH ATTACHMENTS
elseif($_GET['f'] == 'messages'){
  try {
      // Get user ID through authentication
      $user_id = auth_user();
      error_log("User authenticated for messages endpoint: user_id = $user_id");

      // Get ticket ID from POST data
      $data = json_decode(file_get_contents('php://input'), true);
      $ticket_id = isset($data['id']) ? $data['id'] : $_GET['id'];

      if(!$ticket_id) {
          $reply['error'] = 3; // Missing ticket ID
          $reply['message'] = "Missing ticket ID";
          die(json_encode($reply));
      }

      error_log("Fetching messages for ticket ID: $ticket_id");

      // Optional: Add a new message if provided in GET (for backward compatibility)
      if(isset($_GET['msg']) && trim($_GET['msg']) != '') {
          $message = $_GET['msg'];
          error_log("Adding message via GET parameter");

          // Start transaction
          $pdo->beginTransaction();

          // Insert message
          $sth = $pdo->prepare("INSERT INTO ticket_messages
                             (ticket_id, user_id, type, message, created_at)
                             VALUES
                             (:ticket_id, :user_id, 'customer', :message, NOW())");

          $sth->bindValue(':ticket_id', $ticket_id);
          $sth->bindValue(':user_id', $user_id);
          $sth->bindValue(':message', $message);
          $sth->execute();

          // Update ticket's last_reply and last_reply_by
          $sth = $pdo->prepare("UPDATE tickets
                             SET last_reply = NOW(), last_reply_by = 'user', status = 'Open'
                             WHERE id = :ticket_id");

          $sth->bindValue(':ticket_id', $ticket_id);
          $sth->execute();

          // Commit transaction
          $pdo->commit();
          error_log("Message added via GET parameter");
      }

      // Check if ticket exists and belongs to the user
      $sth = $pdo->prepare("SELECT * FROM tickets WHERE id = :ticket_id AND user_id = :user_id");
      $sth->bindValue(':ticket_id', $ticket_id);
      $sth->bindValue(':user_id', $user_id);
      $sth->execute();

      if($sth->rowCount() != 1){
          // Empty response if ticket not found or doesn't belong to user
          $reply['error'] = 4;
          $reply['message'] = "Ticket not found or access denied";
          error_log("Ticket not found or access denied for user $user_id");
          die(json_encode($reply));
      }

      // Create ticket_messages table if it doesn't exist
      $pdo->exec("CREATE TABLE IF NOT EXISTS `ticket_messages` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `ticket_id` int(11) NOT NULL,
          `user_id` int(11) NOT NULL,
          `type` enum('customer','zet') NOT NULL,
          `message` text NOT NULL,
          `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          KEY `ticket_id` (`ticket_id`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");

      // Create ticket_attachments table if it doesn't exist
      $pdo->exec("CREATE TABLE IF NOT EXISTS `ticket_attachments` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `message_id` int(11) NOT NULL,
          `file_name` varchar(255) NOT NULL,
          `file_path` varchar(255) NOT NULL,
          `file_url` varchar(255) NOT NULL,
          `file_type` varchar(100) DEFAULT NULL,
          `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          KEY `message_id` (`message_id`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");

      // Check if the ticket_attachments table has been created successfully
      $tableCheck = $pdo->query("SHOW TABLES LIKE 'ticket_attachments'");
      $hasAttachments = $tableCheck->rowCount() > 0;
      error_log("Attachments table exists: " . ($hasAttachments ? "yes" : "no"));

      // Get messages with attachments if the table exists
      if ($hasAttachments) {
          // Use LEFT JOIN to get messages with their attachments
          $sth = $pdo->prepare("
              SELECT
                  m.id,
                  m.type,
                  m.message,
                  DATE_FORMAT(m.created_at, '%H:%i') as time,
                  a.file_url as attachment
              FROM
                  ticket_messages m
              LEFT JOIN
                  ticket_attachments a ON m.id = a.message_id
              WHERE
                  m.ticket_id = :ticket_id
              ORDER BY
                  m.created_at ASC
          ");

          $sth->bindValue(':ticket_id', $ticket_id);
          $sth->execute();
          error_log("Fetching messages with attachments");
      } else {
          // Fall back to simple message query
          $sth = $pdo->prepare("
              SELECT
                  m.id,
                  m.type,
                  m.message,
                  DATE_FORMAT(m.created_at, '%H:%i') as time,
                  NULL as attachment
              FROM
                  ticket_messages m
              WHERE
                  m.ticket_id = :ticket_id
              ORDER BY
                  m.created_at ASC
          ");

          $sth->bindValue(':ticket_id', $ticket_id);
          $sth->execute();
          error_log("Fetching messages without attachments");
      }

      $messages = array();
      if ($sth) {
          while($row = $sth->fetch(PDO::FETCH_ASSOC)){
              $messages[] = array(
                  'id' => $row['id'],
                  'type' => $row['type'],
                  'time' => $row['time'],
                  'message' => $row['message'],
                  'attachment' => $row['attachment']
              );
          }
      }

      error_log("Found " . count($messages) . " messages for ticket $ticket_id");

      // If no messages found, add a default system message
      if(empty($messages)) {
          $messages[] = array(
              'id' => '1',
              'type' => 'zet',
              'time' => date('H:i'),
              'message' => 'Ticket created. Our support team will respond as soon as possible.',
              'attachment' => null
          );
          error_log("No messages found, added default system message");
      }

      die(json_encode($messages));

  } catch (Exception $e) {
      error_log("Error in messages endpoint: " . $e->getMessage());
      error_log("Exception trace: " . $e->getTraceAsString());
      $reply['error'] = 6;
      $reply['message'] = "Server error: " . $e->getMessage();
      die(json_encode($reply));
  }
}


// GET USER TICKETS LIST
elseif($_GET['f'] == 'tickets'){
  error_log("tickets endpoint called");

  try {
      // Get user ID through authentication
      $data = json_decode(file_get_contents('php://input'), true);

      if (!isset($data['token'])) {
          error_log("No token provided in request body");
          $reply['error'] = 5;
          $reply['message'] = "Authentication required";
          die(json_encode($reply));
      }

      error_log("Using token: " . substr($data['token'], 0, 10) . "...");

      // Call auth_user which will authenticate and return user_id or exit with error
      $user_id = auth_user();
      error_log("User authenticated: user_id = $user_id");

      // Check if tickets table exists
      $tableExists = $pdo->query("SHOW TABLES LIKE 'tickets'");
      if($tableExists->rowCount() == 0) {
          // Create tickets table if it doesn't exist
          $createTable = "CREATE TABLE IF NOT EXISTS `tickets` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) NOT NULL,
              `subject` varchar(255) NOT NULL,
              `department` varchar(50) NOT NULL DEFAULT '24/7 NOC',
              `status` varchar(20) NOT NULL DEFAULT 'Open',
              `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
              `last_reply` timestamp NULL DEFAULT NULL,
              `last_reply_by` enum('user','staff') DEFAULT NULL,
              `priority` varchar(20) DEFAULT 'Low',
              PRIMARY KEY (`id`),
              KEY `user_id` (`user_id`)
          ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

          $pdo->exec($createTable);
          error_log("Created tickets table");
      }

      // Get all tickets for the user
      $sth = $pdo->prepare("SELECT t.*,
                           DATE_FORMAT(t.last_reply, '%H:%i %d.%m.%Y') as last_reply
                           FROM tickets t
                           WHERE t.user_id = :user_id
                           ORDER BY t.last_reply DESC");

      $sth->bindValue(':user_id', $user_id);
      $sth->execute();

      $tickets = array();
      while($row = $sth->fetch(PDO::FETCH_ASSOC)){
          $tickets[] = array(
              'id' => $row['id'],
              'subject' => $row['subject'],
              'department' => $row['department'],
              'last_reply' => $row['last_reply'],
              'status' => $row['status']
          );
      }

      error_log("Returning " . count($tickets) . " tickets for user $user_id");
      die(json_encode($tickets));

  } catch (Exception $e) {
      error_log("Error in tickets endpoint: " . $e->getMessage());
      error_log("Stack trace: " . $e->getTraceAsString());

      $reply['error'] = 6; // Database error
      $reply['message'] = "Failed to get tickets: " . $e->getMessage();
      die(json_encode($reply));
  }
}

// CLOSE TICKET
elseif($_GET['f'] == 'close_ticket'){
  $user_id = auth_user();
  $data = json_decode(file_get_contents('php://input'), true);

  if(!isset($data['ticket_id'])) {
      $reply['error'] = 3; // Missing ticket ID
      die(json_encode($reply));
  }

  $ticket_id = $data['ticket_id'];

  try {
      // Check if ticket exists and belongs to the user
      $sth = $pdo->prepare("SELECT * FROM tickets WHERE id = :ticket_id AND user_id = :user_id");
      $sth->bindValue(':ticket_id', $ticket_id);
      $sth->bindValue(':user_id', $user_id);
      $sth->execute();

      if($sth->rowCount() != 1){
          $reply['error'] = 4; // Ticket not found or doesn't belong to user
          die(json_encode($reply));
      }

      // Close ticket
      $sth = $pdo->prepare("UPDATE tickets SET status = 'Closed' WHERE id = :ticket_id");
      $sth->bindValue(':ticket_id', $ticket_id);
      $sth->execute();

      // Add system message about closing
      $sth = $pdo->prepare("INSERT INTO ticket_messages
                           (ticket_id, user_id, type, message, created_at)
                           VALUES
                           (:ticket_id, :user_id, 'zet', 'Ticket closed by customer', NOW())");

      $sth->bindValue(':ticket_id', $ticket_id);
      $sth->bindValue(':user_id', $user_id);
      $sth->execute();

      // Success response
      $reply['success'] = true;
      die(json_encode($reply));

  } catch (Exception $e) {
      error_log("Error closing ticket: " . $e->getMessage());

      $reply['error'] = 6; // Database error
      $reply['message'] = "Failed to close ticket: " . $e->getMessage();
      die(json_encode($reply));
  }
}

// GET USER SERVICES FOR DROPDOWN
elseif($_GET['f'] == 'user_services'){
  $user_id = auth_user();

  try {
      // Get user's active services
      $sth = $pdo->prepare("SELECT id, label, order_type, status FROM orders
                           WHERE owner_id = :user_id
                           AND status NOT IN ('Cancelled', 'Expired')
                           ORDER BY id DESC");

      $sth->bindValue(':user_id', $user_id);
      $sth->execute();

      $services = array();
      while($row = $sth->fetch(PDO::FETCH_ASSOC)){
          $services[] = array(
              'id' => $row['id'],
              'name' => $row['label'] . ' (' . ucfirst($row['order_type']) . ')',
              'type' => $row['order_type'],
              'status' => $row['status']
          );
      }

      die(json_encode($services));

  } catch (Exception $e) {
      error_log("Error getting user services: " . $e->getMessage());
      die(json_encode([]));
  }
}



?>