import React, { useState, useEffect, useRef } from 'react';
import {
  Search,
  Plus,
  Filter,
  ArrowUp,
  ArrowDown,
  Eye,
  Download,
  XCircle,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  Clock,
  File,
  FileText,
  Edit,
} from 'lucide-react';
import Sidebar from '../components/Sidebar';
import TopMenu from '../components/TopMenu';
import InvoiceModal from '../components/InvoiceModal';
import InvoiceView from '../components/InvoiceView';
const InvoicesPage = ({ navigateTo, sidebarCollapsed, toggleSidebar }) => {
  // State for invoices data
  const [invoices, setInvoices] = useState([]);
  const [allInvoices, setAllInvoices] = useState([]);
  const [invoiceStats, setInvoiceStats] = useState([]);
  const [financialSummary, setFinancialSummary] = useState({
    total_amount: '€0.00',
    paid_amount: '€0.00',
    pending_amount: '€0.00',
    total_change: '+0%',
    paid_change: '+0%'
  });

  // State for filters
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('All');
  const [selectedMonth, setSelectedMonth] = useState('All');

  // State for sorting
  const [sortField, setSortField] = useState('date');
  const [sortDirection, setSortDirection] = useState('desc');

  // State for UI interactions
  const [lastUpdated, setLastUpdated] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // State for pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalPages, setTotalPages] = useState(1);

  // State for edit/create modal
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [editModalMode, setEditModalMode] = useState('create'); // 'create' or 'edit'
  const [selectedInvoice, setSelectedInvoice] = useState(null);

  // State for view-only modal using InvoiceView
  const [viewModalOpen, setViewModalOpen] = useState(false);
  const [viewingInvoice, setViewingInvoice] = useState(null);

  // State for search and autocomplete
  const [searchResults, setSearchResults] = useState([]);
  const [isSearching, setIsSearching] = useState(false);

  // State for filter options
  const [uniqueClients, setUniqueClients] = useState(['All']);
  const [uniqueStatuses, setUniqueStatuses] = useState(['All']);
  const [uniqueMonths, setUniqueMonths] = useState(['All']);

  // Load initial data
  useEffect(() => {
    loadAllData();

    // Check if we need to open a specific invoice (from search)
    const openInvoiceId = localStorage.getItem('open_invoice_id');
    if (openInvoiceId) {
      // Clear the localStorage item to prevent reopening on page refresh
      localStorage.removeItem('open_invoice_id');

      // Find the invoice in the data
      const findAndOpenInvoice = async () => {
        try {
          // Wait for the initial data load to complete
          await loadAllData();

          // Find the invoice in the loaded data first
          // Convert both to strings for comparison to handle numeric IDs
          const foundInvoice = allInvoices.find(inv => String(inv.id) === String(openInvoiceId));

          if (foundInvoice) {
            console.log("Found invoice in loaded data:", foundInvoice);
            setViewingInvoice(foundInvoice);
            setViewModalOpen(true);
          } else {
            // If not found in loaded data, search through all invoices again
            console.log("Invoice not found in loaded data, searching through all invoices");

            // Wait for all invoices to be loaded
            await fetchAllInvoices();

            // Try to find the invoice in the loaded data again
            const foundInvoiceInAll = allInvoices.find(inv => String(inv.id) === String(openInvoiceId));

            if (foundInvoiceInAll) {
              console.log("Found invoice in all invoices:", foundInvoiceInAll);
              setViewingInvoice(foundInvoiceInAll);
              setTimeout(() => {
                setViewModalOpen(true);
              }, 500); // Add a small delay to ensure the component is fully rendered
              return;
            }

            // If still not found, create a minimal invoice object with the ID
            console.log("Invoice not found in any data, creating minimal object");
            const minimalInvoice = {
              id: openInvoiceId,
              client: "Loading...",
              clientId: "",
              clientEmail: "",
              date: new Date().toISOString().split('T')[0],
              dueDate: new Date().toISOString().split('T')[0],
              amount: "€0.00",
              status: "Loading...",
              items: []
            };

            setViewingInvoice(minimalInvoice);
            setTimeout(() => {
              setViewModalOpen(true);
            }, 500);
          }
        } catch (error) {
          console.error("Error fetching invoice:", error);
        }
      };

      findAndOpenInvoice();
    }
  }, []);

  // Effect for applying filters and sorting whenever relevant state changes
  useEffect(() => {
    if (allInvoices.length > 0) {
      applyFilters();
    }
  }, [sortField, sortDirection, selectedStatus, selectedMonth, searchQuery, currentPage, allInvoices]);

  // Separate effect to handle opening an invoice from search
  // This prevents the infinite loop by not depending on the full allInvoices array
  useEffect(() => {
    // Check if we need to open a specific invoice (fallback mechanism)
    const openInvoiceId = localStorage.getItem('open_invoice_id');
    if (openInvoiceId && allInvoices.length > 0) {
      // Only process this once by removing it immediately
      localStorage.removeItem('open_invoice_id');

      console.log("Checking for invoice ID:", openInvoiceId);

      // Use a ref to track if we've already tried to open this invoice
      const invoiceIdRef = useRef(openInvoiceId);

      // Only proceed if we haven't already tried to open this invoice
      if (invoiceIdRef.current === openInvoiceId) {
        invoiceIdRef.current = null; // Mark as processed

        // Find the invoice in the loaded data
        // Convert both to strings for comparison to handle numeric IDs
        const foundInvoice = allInvoices.find(inv => String(inv.id) === String(openInvoiceId));

        if (foundInvoice) {
          console.log("Found invoice in loaded data:", foundInvoice);
          setViewingInvoice(foundInvoice);
          setViewModalOpen(true);
        } else {
          // Create a minimal invoice object with the ID
          console.log("Invoice not found in any data, creating minimal object");
          const minimalInvoice = {
            id: openInvoiceId,
            client: "Loading...",
            clientId: "",
            clientEmail: "",
            date: new Date().toISOString().split('T')[0],
            dueDate: new Date().toISOString().split('T')[0],
            amount: "€0.00",
            status: "Loading...",
            items: []
          };

          setViewingInvoice(minimalInvoice);
          setViewModalOpen(true);
        }
      }
    }
  }, [allInvoices.length]);

  const isEmptyResponse = (text) => {
    return !text || /^\s*$/.test(text);
  };

  const fetchAllInvoices = async () => {
    try {
      let url = `/api_admin_invoices.php?f=get_invoices&sortField=${sortField}&sortDirection=${sortDirection}`;

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token')
        })
      });

      const responseText = await response.text();
      console.log("Raw invoices response:", JSON.stringify(responseText).substring(0, 100) + "...");

      // Check if response is empty or just whitespace
      if (isEmptyResponse(responseText)) {
        console.log("Empty invoices response, using empty array");
        setAllInvoices([]);
        applyFilters([]);
        return;
      }

      // Try to parse the response as JSON
      let data;
      try {
        data = JSON.parse(responseText.trim());
      } catch (parseError) {
        console.error("Failed to parse invoices response:", parseError);
        console.log("Problematic response:", responseText);
        // Return empty array as fallback
        setAllInvoices([]);
        applyFilters([]);
        return;
      }

      // Check if data is an array
      if (!Array.isArray(data)) {
        console.error("Invoice data is not an array:", data);
        setAllInvoices([]);
        applyFilters([]);
        return;
      }

      console.log("Loaded", data.length, "total invoices");

      // Ensure each invoice has proper payment date if paid
      const processedData = data.map(invoice => {
        if (invoice.status === 'Paid' && !invoice.paymentDate) {
          return {
            ...invoice,
            paymentDate: new Date().toISOString().split('T')[0]
          };
        }
        return invoice;
      });

      // Store all invoices and set filtered invoices initially to all
      setAllInvoices(processedData);

      // Apply initial filters (will also set pagination)
      applyFilters(processedData);

    } catch (err) {
      console.error("Error fetching all invoices:", err);
      setAllInvoices([]);
      applyFilters([]);
    }
  };

  // Function to load all initial data
  const loadAllData = async () => {
    setLoading(true);
    setError(null);

    try {
      // Run all fetches in parallel but handle each one's errors separately
      const results = await Promise.allSettled([
        fetchFilterOptions(),
        fetchAllInvoices(),
        fetchInvoiceStats(),
        fetchFinancialSummary()
      ]);

      // Log results of each operation
      results.forEach((result, index) => {
        const operations = ['filter options', 'invoices', 'invoice stats', 'financial summary'];
        if (result.status === 'rejected') {
          console.error(`Error loading ${operations[index]}:`, result.reason);
        }
      });

      setLastUpdated(`${new Date().getHours()}:${String(new Date().getMinutes()).padStart(2, '0')}`);
    } catch (err) {
      console.error("Error initializing data:", err);
      setError("Some data could not be loaded. Please try refreshing the page.");
    } finally {
      setLoading(false);
    }
  };


  const fetchFilterOptions = async () => {
    try {
      const response = await fetch(`/api_admin_invoices.php?f=get_filter_options`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token')
        })
      });

      const responseText = await response.text();
      console.log("Raw filter options response:", JSON.stringify(responseText));

      // Check if response is empty or just whitespace
      if (isEmptyResponse(responseText)) {
        console.log("Empty filter options response, using defaults");
        setUniqueClients(['All']);
        setUniqueStatuses(['All', 'Paid', 'Pending', 'Overdue', 'Draft']);
        setUniqueMonths(['All']);
        return;
      }

      // Try to parse the response as JSON
      let data;
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        console.error("Failed to parse filter options response:", parseError);
        // Set fallback filter options
        setUniqueClients(['All']);
        setUniqueStatuses(['All', 'Paid', 'Pending', 'Overdue', 'Draft']);
        setUniqueMonths(['All']);
        return;
      }

      // Set filter options with fallbacks if any values are missing
      setUniqueClients(['All', ...(data.clients || [])]);
      setUniqueStatuses(['All', ...(data.statuses || ['Paid', 'Pending', 'Overdue', 'Draft'])]);
      setUniqueMonths(['All', ...(data.months || [])]);

    } catch (err) {
      console.error("Error fetching filter options:", err);
      // Set fallback filter options
      setUniqueClients(['All']);
      setUniqueStatuses(['All', 'Paid', 'Pending', 'Overdue', 'Draft']);
      setUniqueMonths(['All']);
    }
  };

  // Function to fetch ALL invoices without any filters
  const fetchInvoiceStats = async () => {
    try {
      const response = await fetch(`/api_admin_invoices.php?f=get_invoice_stats`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token')
        })
      });

      const responseText = await response.text();
      console.log("Raw invoice stats response:", JSON.stringify(responseText));

      // Use default data if response is empty
      if (isEmptyResponse(responseText)) {
        console.log("Empty invoice stats response, using defaults");
        setInvoiceStats([
          {
            title: 'Total Invoices',
            value: '0',
            change: '+0%',
            period: 'since last month',
            icon_class: 'text-indigo-700'
          },
          {
            title: 'Paid',
            value: '0',
            change: '+0%',
            period: 'since last month',
            icon_class: 'text-success'
          },
          {
            title: 'Pending',
            value: '0',
            change: '+0%',
            period: 'since last month',
            icon_class: 'text-warning'
          },
          {
            title: 'Overdue',
            value: '0',
            change: '+0%',
            period: 'since last month',
            icon_class: 'text-danger'
          }
        ]);
        return;
      }

      // Try to parse the response as JSON
      let data;
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        console.error("Failed to parse invoice stats response:", parseError);

        // Provide fallback data
        setInvoiceStats([
          {
            title: 'Total Invoices',
            value: '0',
            change: '+0%',
            period: 'since last month',
            icon_class: 'text-indigo-700'
          },
          {
            title: 'Paid',
            value: '0',
            change: '+0%',
            period: 'since last month',
            icon_class: 'text-success'
          },
          {
            title: 'Pending',
            value: '0',
            change: '+0%',
            period: 'since last month',
            icon_class: 'text-warning'
          },
          {
            title: 'Overdue',
            value: '0',
            change: '+0%',
            period: 'since last month',
            icon_class: 'text-danger'
          }
        ]);
        return;
      }

      setInvoiceStats(data);
    } catch (err) {
      console.error("Error fetching invoice stats:", err);
      // Don't throw the error, use fallback data instead
      setInvoiceStats([
        {
          title: 'Total Invoices',
          value: '0',
          change: '+0%',
          period: 'since last month',
          icon_class: 'text-indigo-700'
        },
        {
          title: 'Paid',
          value: '0',
          change: '+0%',
          period: 'since last month',
          icon_class: 'text-success'
        },
        {
          title: 'Pending',
          value: '0',
          change: '+0%',
          period: 'since last month',
          icon_class: 'text-warning'
        },
        {
          title: 'Overdue',
          value: '0',
          change: '+0%',
          period: 'since last month',
          icon_class: 'text-danger'
        }
      ]);
    }
  };

  // Function to apply filters to the loaded invoices (client-side only)
  const applyFilters = (data = allInvoices) => {
    setLoading(true);

    let filtered = [...data];

    // Apply status filter
    if (selectedStatus !== 'All') {
      filtered = filtered.filter(invoice =>
        invoice.status === selectedStatus
      );
      console.log("After status filtering:", filtered.length, "invoices remain");
    }

    // Apply month filter
    if (selectedMonth !== 'All') {
      filtered = filtered.filter(invoice =>
        getMonthYear(invoice.date) === selectedMonth
      );
      console.log("After month filtering:", filtered.length, "invoices remain");
    }

    // Apply search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(invoice =>
        invoice.id.toLowerCase().includes(query) ||
        invoice.client.toLowerCase().includes(query) ||
        invoice.amount.toLowerCase().includes(query) ||
        (invoice.clientEmail && invoice.clientEmail.toLowerCase().includes(query))
      );
      console.log("After search filtering:", filtered.length, "invoices remain");
    }

    // Sort results
    filtered.sort((a, b) => {
      let valA, valB;

      // Get the values to compare based on sort field
      if (sortField === 'id') {
        // Handle case where id might be a number or undefined
        valA = typeof a.id === 'string' ? a.id.replace('#', '') : (a.id || '');
        valB = typeof b.id === 'string' ? b.id.replace('#', '') : (b.id || '');
      } else if (sortField === 'client') {
        valA = a.client || '';
        valB = b.client || '';
      } else if (sortField === 'amount') {
        // Handle case where amount might be undefined or not a string
        valA = typeof a.amount === 'string' ? parseFloat(a.amount.replace('€', '').replace(',', '')) : 0;
        valB = typeof b.amount === 'string' ? parseFloat(b.amount.replace('€', '').replace(',', '')) : 0;
      } else if (sortField === 'status') {
        valA = a.status || '';
        valB = b.status || '';
      } else if (sortField === 'date') {
        // Handle case where date might be undefined
        valA = a.date ? new Date(a.date) : new Date(0);
        valB = b.date ? new Date(b.date) : new Date(0);
      } else if (sortField === 'dueDate') {
        // Handle case where dueDate might be undefined
        valA = a.dueDate ? new Date(a.dueDate) : new Date(0);
        valB = b.dueDate ? new Date(b.dueDate) : new Date(0);
      } else {
        // Handle case where the field might be undefined
        valA = a[sortField] !== undefined ? a[sortField] : '';
        valB = b[sortField] !== undefined ? b[sortField] : '';
      }

      // Compare and apply sort direction
      if (valA < valB) return sortDirection === 'asc' ? -1 : 1;
      if (valA > valB) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });

    // Calculate pagination
    const totalItems = filtered.length;
    const calculatedTotalPages = Math.ceil(totalItems / itemsPerPage);
    setTotalPages(calculatedTotalPages);

    // Adjust current page if it's out of bounds
    if (currentPage > calculatedTotalPages) {
      setCurrentPage(1);
    }

    // Apply pagination
    const startIndex = (currentPage - 1) * itemsPerPage;
    const paginatedInvoices = filtered.slice(startIndex, startIndex + itemsPerPage);

    // Update the filtered invoices state
    setInvoices(paginatedInvoices);
    setLoading(false);
  };

  const fetchFinancialSummary = async () => {
    try {
      const response = await fetch(`/api_admin_invoices.php?f=get_financial_summary`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token')
        })
      });

      const responseText = await response.text();
      console.log("Raw financial summary response:", JSON.stringify(responseText));

      // Use default data if response is empty
      if (isEmptyResponse(responseText)) {
        console.log("Empty financial summary response, using defaults");
        setFinancialSummary({
          total_amount: '€0.00',
          paid_amount: '€0.00',
          pending_amount: '€0.00',
          total_change: '+0%',
          paid_change: '+0%'
        });
        return;
      }

      // Try to parse the response as JSON
      let data;
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        console.error("Failed to parse financial summary response:", parseError);

        // Return fallback data
        setFinancialSummary({
          total_amount: '€0.00',
          paid_amount: '€0.00',
          pending_amount: '€0.00',
          total_change: '+0%',
          paid_change: '+0%'
        });
        return;
      }

      setFinancialSummary(data);
    } catch (err) {
      console.error("Error fetching financial summary:", err);
      // Don't throw the error, use fallback data instead
      setFinancialSummary({
        total_amount: '€0.00',
        paid_amount: '€0.00',
        pending_amount: '€0.00',
        total_change: '+0%',
        paid_change: '+0%'
      });
    }
  };

  // Search users for autocomplete
  const handleClientSearch = async (query) => {
    if (!query || query.length < 2) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);

    try {
      const response = await fetch(`/api_admin_invoices.php?f=search_users&q=${encodeURIComponent(query)}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token')
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (Array.isArray(data)) {
        setSearchResults(data);
      } else {
        setSearchResults([]);
      }
    } catch (err) {
      console.error("Error searching users:", err);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

// Handle save/create invoice
// Handle save/create invoice
const handleSaveInvoice = async (invoiceData) => {
  try {
    if (editModalMode === 'create') {
      // Create new invoice
      if (!invoiceData.client) {
        alert('Please select a client');
        return;
      }

      if (invoiceData.items.some(item => !item.description || !item.unitPrice)) {
        alert('Please fill in all item details');
        return;
      }

      // Debug - log the data being sent
      console.log("Invoice data to be sent:", invoiceData);

      // Prepare payload with comprehensive details
      const payload = {
        token: localStorage.getItem('admin_token'),
        amount: invoiceData.amount,
        notes: invoiceData.notes || '',
        isProforma: invoiceData.isProforma ? 1 : 0, // Add proforma flag to the payload
        items: invoiceData.items.map(item => ({
          description: item.description,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          total: item.total
        }))
      };

      // Important: Add client information correctly
      payload.clientId = invoiceData.clientId;
      payload.client = invoiceData.client;
      payload.clientEmail = invoiceData.clientEmail;

      // Debug - log the actual payload being sent
      console.log("Final payload:", payload);

      const response = await fetch(`/api_admin_invoices.php?f=create_invoice`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });

      const responseText = await response.text();

      let result;
      try {
        result = JSON.parse(responseText);
      } catch (parseError) {
        console.error('Failed to parse JSON response:', parseError);
        console.log('Raw response:', responseText);
        alert('Unexpected server response: ' + responseText);
        return;
      }

      if (!response.ok || !result.success) {
        throw new Error(result.error || 'Failed to create invoice');
      }

      alert('Invoice created successfully!');

      // Close modal and refresh data when creating
      closeEditModal();
      loadAllData();

    } else if (editModalMode === 'edit') {
      // Update existing invoice
      if (invoiceData.items.some(item => !item.description || !item.unitPrice)) {
        alert('Please fill in all required fields');
        return;
      }

      // Check if status has changed
      const statusChanged = selectedInvoice && selectedInvoice.status !== invoiceData.status;
      const originalStatus = selectedInvoice ? selectedInvoice.status : null;
      const newStatus = invoiceData.status;

      // Check if proforma status has changed
      const proformaChanged = selectedInvoice && selectedInvoice.isProforma !== invoiceData.isProforma;

      console.log(`Status changed: ${statusChanged}, from ${originalStatus} to ${newStatus}`);
      console.log(`Proforma status changed: ${proformaChanged}, to ${invoiceData.isProforma}`);

      // Updated payload to match the PHP endpoint requirements
      const contentPayload = {
        token: localStorage.getItem('admin_token'),
        id: invoiceData.id,
        notes: invoiceData.notes || '',
        isProforma: invoiceData.isProforma ? 1 : 0, // Include proforma flag
        items: invoiceData.items.map(item => ({
          description: item.description,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          total: item.total
        })),
        date: invoiceData.date,
        dueDate: invoiceData.dueDate,
        payment_method: invoiceData.paymentMethod
      };

      // First, update the invoice content using update_invoice_content endpoint
      const contentResponse = await fetch(`/api_admin_invoices.php?f=update_invoice_content`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(contentPayload)
      });

      if (!contentResponse.ok) {
        const errorText = await contentResponse.text();
        console.error("Error response from API:", errorText);
        throw new Error(`Failed to update invoice content: ${errorText}`);
      }

      let contentResult;
      try {
        contentResult = await contentResponse.json();
      } catch (parseError) {
        console.error("Failed to parse response:", parseError);
        throw new Error("Invalid response from server");
      }

      if (!contentResult.success) {
        throw new Error(contentResult.error || 'Failed to update invoice content');
      }

      // Now, if the status has changed, update it separately
      let statusUpdateSuccess = true;
      let statusUpdateError = null;

      if (statusChanged) {
        try {
          // Use the update_invoice_status endpoint directly
          const statusResponse = await fetch(`/api_admin_invoices.php?f=update_invoice_status`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              token: localStorage.getItem('admin_token'),
              id: invoiceData.id,
              status: newStatus
            })
          });

          if (!statusResponse.ok) {
            throw new Error(`HTTP error ${statusResponse.status}`);
          }

          const statusResult = await statusResponse.json();

          if (!statusResult.success) {
            throw new Error(statusResult.error || 'Failed to update invoice status');
          }

          // Check if this was a proforma conversion
          if (statusResult.type === 'conversion') {
            alert(`Proforma invoice has been converted to regular invoice #${statusResult.new_id}`);

            // Close the edit modal
            closeEditModal();

            // Handle the special case of conversion
            handleProformaConversion(statusResult.old_id, statusResult.new_id);
            return;
          }

        } catch (statusError) {
          statusUpdateSuccess = false;
          statusUpdateError = statusError;
          console.error("Error updating status:", statusError);
        }
      }

      // Create an updated invoice object with the latest data
      const updatedInvoice = {
        ...selectedInvoice,
        ...invoiceData,
        // If API returned specific data, use that
        ...(contentResult.items && { items: contentResult.items }),
        ...(contentResult.status && { status: contentResult.status }),
        ...(contentResult.date && { date: contentResult.date }),
        ...(contentResult.dueDate && { dueDate: contentResult.dueDate })
      };

      // Close the edit modal
      closeEditModal();

      // Update the viewingInvoice state with the updated data
      setViewingInvoice(updatedInvoice);

      // Open the view modal to show the updated invoice
      setViewModalOpen(true);

      // Refresh data in the background
      loadAllData();

      // Show appropriate message based on results
      if (statusChanged) {
        if (statusUpdateSuccess) {
          alert('Invoice updated successfully with new status!');
        } else {
          alert(`Invoice content updated successfully, but status change failed: ${statusUpdateError?.message || 'Unknown error'}`);
        }
      } else {
        alert('Invoice updated successfully!');
      }
    }

  } catch (err) {
    console.error("Error saving invoice:", err);
    alert('Failed to save invoice: ' + err.message);
  }
};

  // Special handler for proforma conversion
  const handleProformaConversion = async (oldId, newId) => {
    console.log(`Converting proforma ${oldId} to invoice ${newId}`);

    // Show a single alert message for the conversion
    alert(`Proforma invoice #${oldId} has been paid and converted to regular invoice #${newId}`);

    // Close any open modal first to completely reset the view
    setViewModalOpen(false);
    setViewingInvoice(null);

    // Optimistic UI update - update lists with the new converted invoice
    setInvoices(prevInvoices =>
      prevInvoices.map(inv =>
        inv.id === oldId
          ? {
              ...inv,
              id: newId,
              isProforma: false,
              status: 'Paid',
              paymentDate: new Date().toISOString().split('T')[0]
            }
          : inv
      )
    );

    setAllInvoices(prevInvoices =>
      prevInvoices.map(inv =>
        inv.id === oldId
          ? {
              ...inv,
              id: newId,
              isProforma: false,
              status: 'Paid',
              paymentDate: new Date().toISOString().split('T')[0]
            }
          : inv
      )
    );

    // Add a longer delay to ensure backend processing is complete
    // This is critical for ensuring the new invoice data is available
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Refresh all data to ensure we have the latest data
    await loadAllData();

    // Fetch the fresh invoice with updated ID to get complete data
    try {
      console.log(`Fetching fresh data for new invoice ${newId}`);

      // Try to find the invoice in the refreshed data first
      const existingInvoice = allInvoices.find(inv => String(inv.id) === String(newId));

      if (existingInvoice) {
        console.log("Found invoice in refreshed data:", existingInvoice);

        // Update the viewing invoice with the found data
        setViewingInvoice({
          ...existingInvoice,
          isProforma: false,
          status: 'Paid',
          paymentDate: existingInvoice.paymentDate || new Date().toISOString().split('T')[0]
        });

        // Open the modal after a short delay
        setTimeout(() => {
          setViewModalOpen(true);
        }, 300);

        return true;
      }

      // If not found in refreshed data, try direct API call
      // Make sure we're using a clean ID format
      let cleanId = newId;
      if (typeof newId === 'string') {
        cleanId = newId.replace(/\D/g, ''); // Remove all non-digits
        cleanId = cleanId.replace(/^0+/, ''); // Remove leading zeros
      }

      console.log(`Using cleaned ID for API request: ${cleanId}`);

      const response = await fetch(`/api_admin_invoices.php?f=get_invoice`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          id: cleanId
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const responseText = await response.text();
      console.log("Raw invoice details response:", responseText);

      // Check if response is empty
      if (!responseText || /^\s*$/.test(responseText)) {
        console.warn("Empty response when fetching converted invoice");
        throw new Error("Empty response from server");
      }

      // Try to parse the response as JSON
      let data;
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        console.error("Failed to parse invoice response:", parseError);

        // If the parsing failed, try to create a synthetic invoice object as fallback
        const freshInvoice = {
          id: newId,
          rawId: newId,
          client: "Client information unavailable",
          clientId: "Unknown",
          date: new Date().toISOString().split('T')[0],
          dueDate: new Date().toISOString().split('T')[0],
          status: "Paid",
          paymentDate: new Date().toISOString().split('T')[0],
          paymentMethod: "Bank Transfer",
          amount: "€0.00",
          items: [],
          subtotal: "€0.00",
          tax: "€0.00",
          isProforma: false,
          notes: "Invoice converted from proforma. Details may be incomplete."
        };

        // Update the viewing invoice and reopen modal with fallback data
        setViewingInvoice(freshInvoice);

        setTimeout(() => {
          setViewModalOpen(true);
        }, 300);

        // No need for another refresh

        return false;
      }

      if (data) {
        console.log("Successfully fetched new invoice data:", data);

        // Create a complete invoice object with all necessary properties
        const updatedInvoice = {
          ...data,
          id: newId,
          isProforma: false,
          status: 'Paid',
          paymentDate: data.paymentDate || new Date().toISOString().split('T')[0]
        };

        // Update the viewing invoice and reopen modal
        setViewingInvoice(updatedInvoice);

        setTimeout(() => {
          setViewModalOpen(true);
        }, 300);

        return true;
      } else {
        console.warn("Failed to fetch fresh data for converted invoice");

        // If we couldn't get fresh data, create a synthetic one from what we know
        const updatedInvoice = {
          id: newId,
          rawId: newId,
          client: "Client information unavailable",
          clientId: "Unknown",
          date: new Date().toISOString().split('T')[0],
          dueDate: new Date().toISOString().split('T')[0],
          status: "Paid",
          paymentDate: new Date().toISOString().split('T')[0],
          paymentMethod: "Bank Transfer",
          amount: "€0.00",
          items: [],
          subtotal: "€0.00",
          tax: "€0.00",
          isProforma: false,
          notes: "Invoice converted from proforma. Details may be incomplete."
        };

        // Update the viewing invoice with fallback data
        setViewingInvoice(updatedInvoice);

        setTimeout(() => {
          setViewModalOpen(true);
        }, 300);

        // No need for another refresh

        return false;
      }
    } catch (err) {
      console.error("Error handling proforma conversion:", err);

      // Try one more time with a longer delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      // No need to refresh data again, we already did it earlier

      // Try to find the converted invoice in the current data
      const refreshedInvoice = allInvoices.find(inv => String(inv.id) === String(newId));

      if (refreshedInvoice) {
        // If found, show it
        setViewingInvoice(refreshedInvoice);
        setTimeout(() => {
          setViewModalOpen(true);
        }, 300);
        return true;
      } else {
        // Just return without showing an alert or refreshing again
        console.log(`Couldn't find converted invoice #${newId} in the data`);
        return false;
      }
    }
  };

  // Updated method to handle invoice status changes
  const handleUpdateStatus = async (invoiceId, newStatus, conversionData = null, paymentDate = null) => {
    try {
      console.log(`Updating status for invoice ${invoiceId} to ${newStatus}`);
      setLoading(true);

      // CASE 1: Handle proforma conversion
      if (conversionData && conversionData.type === 'conversion') {
        const { old_id, new_id } = conversionData;
        console.log(`Handling proforma conversion: ${old_id} → ${new_id}`);

        // Use the specialized conversion handler
        await handleProformaConversion(old_id, new_id);

        setLoading(false);
        return;
      }

      // CASE 2: Handle regular status updates

      // Get current payment date or use provided one
      const currentPaymentDate = paymentDate ||
        (newStatus === 'Paid' ? new Date().toISOString().split('T')[0] : null);

      // Update UI optimistically
      if (viewingInvoice && viewingInvoice.id === invoiceId) {
        setViewingInvoice(prev => ({
          ...prev,
          status: newStatus,
          paymentDate: currentPaymentDate
        }));
      }

      // Update selected invoice if editing
      if (selectedInvoice && selectedInvoice.id === invoiceId) {
        setSelectedInvoice(prev => ({
          ...prev,
          status: newStatus,
          paymentDate: currentPaymentDate
        }));
      }

      // Update all invoice lists
      setInvoices(prevInvoices =>
        prevInvoices.map(inv =>
          inv.id === invoiceId
            ? { ...inv, status: newStatus, paymentDate: currentPaymentDate }
            : inv
        )
      );

      setAllInvoices(prevInvoices =>
        prevInvoices.map(inv =>
          inv.id === invoiceId
            ? { ...inv, status: newStatus, paymentDate: currentPaymentDate }
            : inv
        )
      );

      // Call the API to update the status
      const response = await fetch(`/api_admin_invoices.php?f=update_invoice_status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          id: invoiceId,
          status: newStatus
        })
      });

      const responseText = await response.text();
      console.log("Status update API response:", responseText);

      // Handle the response
      try {
        const result = JSON.parse(responseText);

        if (!result.success) {
          throw new Error(result.error || 'Failed to update invoice status');
        }

        // Check if the API unexpectedly converted a proforma
        if (result.type === 'conversion' && !conversionData) {
          console.log("Unexpected conversion detected, handling it now");

          // Handle the conversion with specialized function
          await handleProformaConversion(result.old_id, result.new_id);

          // Return early to avoid additional refreshes
          return;
        }

      } catch (parseError) {
        console.error("Failed to parse API response:", parseError);
        throw new Error("Invalid server response");
      }

      // Don't refresh data here if we're handling a proforma conversion
      // as the conversion handler will handle its own data refresh

    } catch (err) {
      console.error("Error updating invoice status:", err);
      alert('Error updating status: ' + err.message);

      // Revert any optimistic updates by refreshing all data
      await loadAllData();
    } finally {
      setLoading(false);
    }
  };
  // Sorting handler - fixed to work with state correctly
  const handleSort = (field) => {
    // If we're clicking the same field, toggle direction
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // If new field, set it and default to ascending
      setSortField(field);
      setSortDirection('asc');
    }

    // Sorting will be applied via useEffect when state changes
  };

  // New pagination handlers
  const handlePreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const handlePageClick = (page) => {
    setCurrentPage(page);
  };

  // Filter handlers - reset to page 1 when filters change
  const handleStatusFilter = (status) => {
    setSelectedStatus(status);
    setCurrentPage(1);
  };

  const handleMonthFilter = (month) => {
    setSelectedMonth(month);
    setCurrentPage(1);
  };

  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1);
  };

  // Add a refresh function that reloads all data
  const handleRefreshData = async () => {
    await loadAllData();
  };

  const getSortIcon = (field) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ?
      <ArrowUp className="w-4 h-4 inline ml-1" /> :
      <ArrowDown className="w-4 h-4 inline ml-1" />;
  };

  // Removed unused fetchInvoiceById function as it's not needed anymore

  // Updated function to open the view modal
  const openViewModal = (invoice) => {
    console.log("Opening view modal for invoice:", invoice);

    // First close any open modal
    setViewModalOpen(false);
    setViewingInvoice(null);

    // InvoiceView will fetch its own data using the ID
    setTimeout(() => {
      setViewingInvoice(invoice);
      setViewModalOpen(true);
    }, 100);
  };

  // Enhanced close view modal function
  const closeViewModal = () => {
    setViewModalOpen(false);
    // Clear viewing invoice with a delay to avoid UI flashing
    setTimeout(() => {
      setViewingInvoice(null);
    }, 100);
  };

  // Enhanced handleEditFromView method
  const handleEditFromView = (invoice) => {
    // First close the view modal
    closeViewModal();

    // Wait for the modal to fully close
    setTimeout(() => {
      // Open edit modal with the invoice
      openEditModal(invoice);
    }, 200);
  };

  // Modal control functions for edit/create
  const openCreateModal = () => {
    setSelectedInvoice(null);
    setEditModalMode('create');
    setEditModalOpen(true);
  };

  const openEditModal = (invoice) => {
    setSelectedInvoice(invoice);
    setEditModalMode('edit');
    setEditModalOpen(true);
  };

  const closeEditModal = () => {
    setEditModalOpen(false);
    setSelectedInvoice(null);
  };

  // Get month and year from date
  const getMonthYear = (dateString) => {
    const date = new Date(dateString);
    return `${date.toLocaleString('default', { month: 'long' })} ${date.getFullYear()}`;
  };

  // Format dates as dd/mm/yyyy for display
  const formatDate = (dateString) => {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return '';
      const day = String(date.getDate()).padStart(2, '0');
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const year = date.getFullYear();
      return `${day}/${month}/${year}`;
    } catch (e) {
      console.error("Error formatting date:", e);
      return '';
    }
  };

  // Calculate due date status
  const getDueDateStatus = (dueDate, status) => {
    if (status === 'Paid' || status === 'Draft') return null;

    const today = new Date();
    const due = new Date(dueDate);
    const diffDays = Math.ceil((due - today) / (1000 * 60 * 60 * 24));

    if (diffDays < 0) {
      return <span className="text-xs text-red-600">{Math.abs(diffDays)} days overdue</span>;
    } else if (diffDays <= 7) {
      return <span className="text-xs text-yellow-600">Due in {diffDays} days</span>;
    }

    return null;
  };

  // Generate array of page numbers to display
  const getPageNumbers = () => {
    const pageNumbers = [];
    const maxPagesToShow = 5;

    // Logic to show a subset of pages if there are many
    if (totalPages <= maxPagesToShow) {
      // Show all pages if there are few
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(i);
      }
    } else {
      // Show a subset with current page in the middle
      let startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));
      let endPage = Math.min(totalPages, startPage + maxPagesToShow - 1);

      // Adjust if we're near the end
      if (endPage === totalPages) {
        startPage = Math.max(1, endPage - maxPagesToShow + 1);
      }

      for (let i = startPage; i <= endPage; i++) {
        pageNumbers.push(i);
      }
    }

    return pageNumbers;
  };

  // Render status badge
  const renderStatusBadge = (status, isProforma = false) => {
    // If this is a proforma invoice, show a special badge
    if (isProforma) {
      return (
        <span className="px-2 py-1 rounded-md text-xs font-medium flex items-center w-fit bg-purple-100 text-purple-800">
          <FileText className="w-4 h-4 mr-1" />
          Proforma
        </span>
      );
    }

    // Standard status badges for regular invoices
    const badgeClasses = {
      'Paid': 'bg-green-100 text-green-800',
      'Pending': 'bg-yellow-100 text-yellow-800',
      'Overdue': 'bg-red-100 text-red-800',
      'Disputed': 'bg-purple-100 text-purple-800',
      'Draft': 'bg-blue-100 text-blue-800'
    };

    const icons = {
      'Paid': <CheckCircle className="w-4 h-4 mr-1" />,
      'Pending': <Clock className="w-4 h-4 mr-1" />,
      'Overdue': <AlertCircle className="w-4 h-4 mr-1" />,
      'Disputed': <AlertCircle className="w-4 h-4 mr-1" />,
      'Draft': <File className="w-4 h-4 mr-1" />
    };

    return (
      <span className={`px-2 py-1 rounded-md text-xs font-medium flex items-center w-fit ${badgeClasses[status] || 'bg-gray-100 text-gray-800'}`}>
        {icons[status] || <AlertCircle className="w-4 h-4 mr-1" />}
        {status}
      </span>
    );
  };

  // Handle empty function stubs for actions not yet implemented
  const handlePrintInvoice = (invoice) => {
    console.log('Printing invoice:', invoice);
    // Implement printing logic
  };

  const handleDownloadInvoice = (invoice) => {
    console.log('Downloading invoice:', invoice);
    // Implement download logic
  };

  const handleSendInvoice = (invoice) => {
    console.log('Sending invoice:', invoice);
    // Implement send logic
  };

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <Sidebar
        sidebarCollapsed={sidebarCollapsed}
        activeMenu="Invoices"
        navigateTo={navigateTo}
        toggleSidebar={toggleSidebar}
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col bg-gray-100">
        {/* Top Menu */}
        <TopMenu toggleSidebar={toggleSidebar} />

        {/* Invoices Content */}
        <div className="p-6 space-y-6 overflow-auto">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-gray-800">Invoices</h1>
            <div className="flex items-center space-x-2">
              <div className="text-sm text-gray-500">Last updated: {lastUpdated || 'Never'}</div>
              <button
                onClick={handleRefreshData}
                className="p-2 text-gray-500 hover:text-indigo-700 rounded-full hover:bg-gray-100"
                disabled={loading}
              >
                <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              </button>
              <button
                onClick={openCreateModal}
                className="bg-indigo-700 hover:bg-indigo-800 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center"
              >
                <Plus className="w-4 h-4 mr-1" />
                Create Invoice
              </button>
            </div>
          </div>

          {/* Invoice Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {loading ? (
              Array(4).fill(0).map((_, index) => (
                <div
                  key={index}
                  className="bg-white p-6 shadow-sm flex items-center justify-between border rounded-md metric-card animate-pulse"
                >
                  <div className="w-full">
                    <div className="h-3 bg-gray-200 rounded w-1/2 mb-2"></div>
                    <div className="h-6 bg-gray-200 rounded w-1/4 mb-2"></div>
                    <div className="h-2 bg-gray-200 rounded w-3/4"></div>
                  </div>
                </div>
              ))
            ) : error ? (
              <div className="col-span-4 p-4 text-center text-red-600">
                {error}
              </div>
            ) : (
              invoiceStats.map((stat, index) => (
                <div
                  key={index}
                  className="bg-white p-6 shadow-sm flex items-center justify-between border rounded-md metric-card"
                >
                  <div>
                    <div className="text-sm text-gray-700">{stat.title}</div>
                    <div className="text-2xl font-bold mt-1">{stat.value}</div>
                    <div className="text-xs text-gray-500 mt-1">
                      <span className={stat.change.startsWith('+') ? 'text-green-500' : 'text-red-500'}>
                        {stat.change}
                      </span> {stat.period}
                    </div>
                  </div>
                  <div className={`card-custom-icon ${stat.icon_class}`}>
                    {stat.title === 'Total Invoices' ? (
                      <FileText className="text-indigo-700" size={40} strokeWidth={2} />
                    ) : stat.title === 'Paid' ? (
                      <CheckCircle className="text-success" size={40} strokeWidth={2} />
                    ) : stat.title === 'Pending' ? (
                      <Clock className="text-warning" size={40} strokeWidth={2} />
                    ) : (
                      <AlertCircle className="text-danger" size={40} strokeWidth={2} />
                    )}
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Invoices Filter and Search */}
          <div className="bg-white border border-gray-200 shadow-sm rounded-md">
            <div className="p-4 border-b flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <div className="flex flex-wrap gap-2">
                <div className="relative">
                  <select
                    className="pl-3 pr-8 py-2 border rounded-md text-sm w-full focus:outline-none focus:ring-2 focus:ring-indigo-200 appearance-none"
                    value={selectedStatus}
                    onChange={(e) => handleStatusFilter(e.target.value)}
                  >
                    {uniqueStatuses.map(status => (
                      <option key={status} value={status}>{status === 'All' ? 'All Status' : status}</option>
                    ))}
                  </select>
                  <Filter className="absolute right-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                </div>

                <div className="relative">
                  <select
                    className="pl-3 pr-8 py-2 border rounded-md text-sm w-full focus:outline-none focus:ring-2 focus:ring-indigo-200 appearance-none"
                    value={selectedMonth}
                    onChange={(e) => handleMonthFilter(e.target.value)}
                  >
                    {uniqueMonths.map(month => (
                      <option key={month} value={month}>{month === 'All' ? 'All Months' : month}</option>
                    ))}
                  </select>
                  <Filter className="absolute right-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                </div>
              </div>
              <div className="relative w-full sm:w-64">
                <input
                  type="text"
                  placeholder="Search invoices..."
                  className="pl-8 pr-3 py-2 border rounded-md text-sm w-full focus:outline-none focus:ring-2 focus:ring-indigo-200 search-input"
                  value={searchQuery}
                  onChange={handleSearch}
                />
                <Search className="absolute left-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
              </div>
            </div>

            {/* Invoices Table */}
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="text-gray-500 text-xs border-b">
                    <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('id')}>
                      INVOICE # {getSortIcon('id')}
                    </th>
                    <th className="p-4 text-left font-medium cursor-pointer hide-xs" onClick={() => handleSort('client')}>
                      CLIENT {getSortIcon('client')}
                    </th>
                    <th className="p-4 text-left font-medium cursor-pointer hide-sm" onClick={() => handleSort('date')}>
                      ISSUE DATE {getSortIcon('date')}
                    </th>
                    <th className="p-4 text-left font-medium cursor-pointer hide-sm" onClick={() => handleSort('dueDate')}>
                      DUE DATE {getSortIcon('dueDate')}
                    </th>
                    <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('amount')}>
                      AMOUNT {getSortIcon('amount')}
                    </th>
                    <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('status')}>
                      STATUS {getSortIcon('status')}
                    </th>

                  </tr>
                </thead>
                <tbody>
                  {loading ? (
                    Array(5).fill(0).map((_, index) => (
                      <tr key={index} className={`border-b ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}>
                        <td className="p-4"><div className="h-4 bg-gray-200 rounded w-16 animate-pulse"></div></td>
                        <td className="p-4"><div className="h-4 bg-gray-200 rounded w-32 animate-pulse"></div></td>
                        <td className="p-4 hide-sm"><div className="h-4 bg-gray-200 rounded w-24 animate-pulse"></div></td>
                        <td className="p-4 hide-sm"><div className="h-4 bg-gray-200 rounded w-24 animate-pulse"></div></td>
                        <td className="p-4"><div className="h-4 bg-gray-200 rounded w-20 animate-pulse"></div></td>
                        <td className="p-4"><div className="h-4 bg-gray-200 rounded w-16 animate-pulse"></div></td>
                        <td className="p-4"><div className="h-4 bg-gray-200 rounded w-24 animate-pulse"></div></td>
                      </tr>
                    ))
                  ) : error ? (
                    <tr>
                      <td colSpan="7" className="p-4 text-center text-red-600">
                        {error}
                      </td>
                    </tr>
                  ) : invoices.length > 0 ? (
                    invoices.map((invoice, index) => (
                      <tr
                        key={`invoice-row-${invoice.id}`}
                        className={`border-b ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} hover:bg-gray-50 task-row cursor-pointer ${invoice.isProforma ? 'bg-purple-50' : ''}`}
                        onClick={(e) => {
                          e.preventDefault();
                          openViewModal(invoice);
                        }}
                      >
                        <td className="p-4 font-medium text-indigo-700">
                          {invoice.isProforma ? 'Proforma ' : ''}{invoice.id}
                        </td>
                        <td className="p-4 text-gray-700">{invoice.client}</td>
                        <td className="p-4 text-gray-700 hide-sm">{formatDate(invoice.date)}</td>
                        <td className="p-4 hide-sm">
                          <div className="flex flex-col">
                            <span className="text-gray-700">{formatDate(invoice.dueDate)}</span>
                            {getDueDateStatus(invoice.dueDate, invoice.status)}
                          </div>
                        </td>
                        <td className="p-4 font-medium">{invoice.amount}</td>
                        <td className="p-4">
                          {renderStatusBadge(invoice.status, invoice.isProforma)}
                        </td>

                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan="7" className="p-4 text-center text-gray-500">
                        No invoices found matching your criteria
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            <div className="p-4 border-t flex justify-between items-center">
              <div className="text-sm text-gray-500">
                Showing {invoices.length} of {allInvoices.length} invoices
              </div>
              <div className="flex space-x-1">
                <button
                  onClick={handlePreviousPage}
                  disabled={currentPage === 1}
                  className={`px-3 py-1 border rounded text-sm ${currentPage === 1 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50'}`}
                >
                  Previous
                </button>

                {getPageNumbers().map(page => (
                  <button
                    key={page}
                    onClick={() => handlePageClick(page)}
                    className={`px-3 py-1 border rounded text-sm ${currentPage === page ? 'bg-indigo-700 text-white' : 'bg-white text-gray-700 hover:bg-gray-50'}`}
                  >
                    {page}
                  </button>
                ))}

                <button
                  onClick={handleNextPage}
                  disabled={currentPage === totalPages}
                  className={`px-3 py-1 border rounded text-sm ${currentPage === totalPages ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50'}`}
                >
                  Next
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* View Modal - Uses the InvoiceView component (with independent data fetching) */}
      {viewModalOpen && viewingInvoice && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            {/* Custom header with close button */}
            <div className="bg-white p-6 border-b flex justify-between items-center">
              <div>
                <h2 className="text-xl font-bold text-gray-800 mb-2">
                  {viewingInvoice.isProforma ? 'Proforma Invoice' : 'Invoice'} #{viewingInvoice.id}
                </h2>
                <div className=" flex flex-wrap gap-2">
                  {/* Type/Status badge - For proforma, we only show one badge */}
                  {viewingInvoice.isProforma ? (
                    <span className="px-2 py-1 rounded-full text-xs font-medium flex items-center w-fit bg-purple-100 text-purple-800">
                      <FileText className="w-4 h-4 mr-1" />
                      Proforma
                    </span>
                  ) : (
                    <>
                      {/* Type badge for regular invoices */}
                      <span className="px-2 py-1 rounded-full text-xs font-medium flex items-center w-fit bg-blue-100 text-blue-800">
                        <FileText className="w-4 h-4 mr-1" />
                        Invoice
                      </span>

                      {/* Status badge for regular invoices */}
                      {viewingInvoice.status && (
                        <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center w-fit ${viewingInvoice.status === 'Paid' ? 'bg-green-100 text-green-800' : viewingInvoice.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' : viewingInvoice.status === 'Overdue' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'}`}>
                          {viewingInvoice.status === 'Paid' ? <CheckCircle className="w-4 h-4 mr-1" /> :
                           viewingInvoice.status === 'Pending' ? <Clock className="w-4 h-4 mr-1" /> :
                           viewingInvoice.status === 'Overdue' ? <AlertCircle className="w-4 h-4 mr-1" /> :
                           <File className="w-4 h-4 mr-1" />}
                          {viewingInvoice.status}
                        </span>
                      )}
                    </>
                  )}

                  {/* Invoice number badge */}
                  <span className="px-2 py-1 rounded-full text-xs font-medium flex items-center w-fit bg-gray-100 text-gray-800">
                    <File className="w-4 h-4 mr-1" />
                    #{viewingInvoice.id}
                  </span>
                </div>
              </div>
              <button
                onClick={closeViewModal}
                className="text-gray-500 hover:text-gray-700"
              >
                <XCircle className="w-5 h-5" />
              </button>
            </div>

            {/* InvoiceView will fetch its own data */}
            <InvoiceView
              invoice={viewingInvoice}
              onEdit={handleEditFromView}
              onUpdateStatus={handleUpdateStatus}
              onSend={handleSendInvoice}
              onPrint={handlePrintInvoice}
              onDownload={handleDownloadInvoice}
              className="border-t-0 rounded-t-none"
              hideHeader={true}
            />
          </div>
        </div>
      )}

      {/* Edit/Create Modal */}
      <InvoiceModal
        isOpen={editModalOpen}
        mode={editModalMode}
        invoice={selectedInvoice}
        onClose={closeEditModal}
        onSave={handleSaveInvoice}
        onUpdateStatus={handleUpdateStatus}
        onSendInvoice={handleSendInvoice}
        onPrint={handlePrintInvoice}
        onDownload={handleDownloadInvoice}
        searchResults={searchResults}
        isSearching={isSearching}
        onClientSearch={handleClientSearch}
        statuses={['Paid', 'Pending', 'Overdue', 'Disputed', 'Draft']}
        paymentMethods={['Bank Transfer', 'Credit Card', 'PayPal', 'Cash', 'Check']}
      />
    </div>
  );
};

export default InvoicesPage;