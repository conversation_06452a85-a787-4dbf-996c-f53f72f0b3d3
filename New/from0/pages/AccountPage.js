import React, { useState, useRef, useEffect } from 'react';
import {
  Save,
  Edit,
  User,
  Mail,
  Phone,
  Building,
  Globe,
  Key,
  Shield,
  Upload,
  Clock,
  Moon,
  Sun,
  Download,
  Languages,
  Cpu,
  Github,
  Slack,
  LogOut,
  AlertTriangle,
  Activity,
  Trash2,
  Settings,
  MessageCircle,
  Send,
  PaperPlane,
  Bell,
  CheckCircle,
  XCircle
} from 'lucide-react';
import Sidebar from '../components/Sidebar';
import TopMenu from '../components/TopMenu';
import { useAuth } from '../AuthContext';
import { API_URL } from '../config';
import avatar from '../assets/default.png';

const AccountPage = ({ navigateTo }) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [activeTab, setActiveTab] = useState('profile');
  const [editMode, setEditMode] = useState(false);
  const [darkMode, setDarkMode] = useState(false);
  const [uploadStatus, setUploadStatus] = useState(null); // 'success', 'error', or null
  const [uploadMessage, setUploadMessage] = useState('');

  const fileInputRef = useRef(null);
  const { adminProfile, fetchAdminProfile } = useAuth();

  // Helper function to get profile picture path
  const getProfilePicturePath = (picturePath) => {
    if (!picturePath || picturePath.trim() === '') {
      console.log('Picture path is empty, using default avatar.');
      return avatar;
    }
  
    console.log(`Original picture path from DB: '${picturePath}'`);
  
    let finalUrl;
    
    // If it's already a full URL, use it as is
    if (picturePath.startsWith('http://') || picturePath.startsWith('https://')) {
      finalUrl = picturePath;
    }
    // For all other cases, serve through PHP API
    else {
      // Ensure the path starts with /
      const cleanPath = picturePath.startsWith('/') ? picturePath : '/' + picturePath;
      
      // Serve image through PHP API
      finalUrl = `${API_URL}/api_admin_profile.php?f=get_profile_image&path=${encodeURIComponent(cleanPath)}`;
    }
  
    console.log(`Constructed profile picture URL: '${finalUrl}'`);
    return finalUrl;
  };


  const handleDeleteProfilePicture = async () => {
    try {
      setUploadStatus(null);
      setUploadMessage('Removing profile picture...');
  
      const response = await fetch(`${API_URL}/api_admin_profile.php?f=delete_profile_picture`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token')
        }),
      });
  
      const data = await response.json();
      console.log('Delete response:', data);
  
      if (data.success) {
        setUploadStatus('success');
        setUploadMessage('Profile picture reset to default');
        // Refresh admin profile to get the updated picture URL
        fetchAdminProfile();
      } else {
        setUploadStatus('error');
        setUploadMessage(data.error || 'Failed to reset profile picture');
      }
    } catch (error) {
      setUploadStatus('error');
      setUploadMessage('An error occurred while resetting the profile picture');
      console.error('Delete error:', error);
    }
  };

  // Function to handle profile picture upload
  const handleProfilePictureUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Check file type
    const validTypes = ['image/jpeg', 'image/png', 'image/gif'];
    if (!validTypes.includes(file.type)) {
      setUploadStatus('error');
      setUploadMessage('Please upload a valid image file (JPEG, PNG, or GIF)');
      return;
    }

    // Check file size (max 2MB)
    if (file.size > 2 * 1024 * 1024) {
      setUploadStatus('error');
      setUploadMessage('Image size should be less than 2MB');
      return;
    }

    // Create form data
    const formData = new FormData();
    formData.append('profile_picture', file);
    formData.append('token', localStorage.getItem('admin_token')); // Add authentication token

    try {
      setUploadStatus(null);
      setUploadMessage('Uploading...');

      console.log('Uploading profile picture with token:', localStorage.getItem('admin_token'));

      // Send the file to the server
      const response = await fetch(`${API_URL}/api_admin_profile.php?f=update_profile_picture`, {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();
      console.log('Upload response:', data);

      if (data.success) {
        setUploadStatus('success');
        setUploadMessage('Profile picture updated successfully');
        // Refresh admin profile to get the updated picture URL
        fetchAdminProfile();
      } else {
        setUploadStatus('error');
        setUploadMessage(data.error || 'Failed to upload profile picture');
      }
    } catch (error) {
      setUploadStatus('error');
      setUploadMessage('An error occurred while uploading the profile picture');
      console.error('Upload error:', error);
    }
  };

  // Trigger file input click
  const triggerFileInput = () => {
    fileInputRef.current.click();
  };

  // Load admin profile data when component mounts or changes
  useEffect(() => {
    if (adminProfile) {
      console.log('Admin profile loaded/updated:', adminProfile);

      // Create a new userData object with the updated profile data
      const updatedUserData = {
        ...userData,
        name: `${adminProfile.first_name} ${adminProfile.last_name}`,
        email: adminProfile.email || '',
        phone: adminProfile.phone || '',
        position: adminProfile.function || '',
        department: adminProfile.department_name || '',
        location: adminProfile.location || '',
        // Format the last_login timestamp
        lastLogin: adminProfile.last_login ? new Date(adminProfile.last_login).toLocaleString() : '',
        // We don't have join date in the database, so we'll use last_login as a fallback
        joinDate: adminProfile.last_login ? new Date(adminProfile.last_login).toLocaleDateString() : '',
      };

      console.log('Setting updated user data:', updatedUserData);
      setUserData(updatedUserData);
    } else {
      console.log('No admin profile available');
    }
  }, [adminProfile]); // Only depend on adminProfile to avoid infinite loops

  // User data - expanded with new fields
  const [userData, setUserData] = useState({
    name: '',
    email: '',
    phone: '',
    position: '',
    department: '',
    location: '',
    joinDate: '',
    lastLogin: '',
    securityLevel: 'Administrator',
    twoFactorEnabled: false,
    apiAccess: false,
    telegramUsername: '',
    preferredLanguage: 'English',
    dateFormat: 'DD/MM/YYYY',
    timeFormat: '24h',
    timezone: 'Europe/Berlin',
    notifications: {
      email: true,
      telegram: false,
      system: true,
      slack: false
    },
    integrations: {
      github: false,
      slack: false,
      jira: false
    },
    recentActivity: [
      { action: 'Login', timestamp: '', location: '', device: '' }
    ]
  });

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const handleSave = async () => {
    if (!adminProfile) {
      setEditMode(false);
      return;
    }

    try {
      // Prepare data to update
      const updateData = {
        token: localStorage.getItem('admin_token'), // Add the authentication token
        id: adminProfile.id,
        first_name: userData.name.split(' ')[0],
        last_name: userData.name.split(' ').slice(1).join(' '),
        email: userData.email,
        function: userData.position,
        // department_id is not editable, but we need to pass it to maintain the existing value
        department_id: adminProfile.department_id,
        location: userData.location,
        phone: userData.phone
      };

      console.log('Sending update data:', updateData);

      // Send update request
      const response = await fetch(`${API_URL}/api_admin_profile.php?f=update_admin_profile`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      const data = await response.json();
      console.log('Update response:', data);

      if (data.success) {
        console.log('Profile updated successfully, refreshing data...');

        // Try a direct update to the userData state as well
        setUserData({
          ...userData,
          name: `${updateData.first_name} ${updateData.last_name}`,
          email: updateData.email,
          phone: updateData.phone || '',
          position: updateData.function || '',
          location: updateData.location || '',
        });

        // Add a small delay before refreshing the profile
        setTimeout(async () => {
          // Refresh admin profile
          const updatedProfile = await fetchAdminProfile();
          console.log('Admin profile refreshed:', updatedProfile);

          // Set edit mode to false
          setEditMode(false);

          // Show success message
          alert('Profile updated successfully!');
        }, 1000); // 1000ms delay (increased from 500ms)
      } else {
        console.error('Failed to update profile:', data.error);
        alert(`Failed to update profile: ${data.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      alert(`Error updating profile: ${error.message || 'Unknown error'}`);
    }
  };

  const renderTabContent = () => {
    switch(activeTab) {
      case 'profile':
        return (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">Profile Information</h2>
              
              <div className="flex space-x-2">
                
                {!editMode && (
                  <button
                    onClick={() => {
                      fetchAdminProfile().then(() => {
                        console.log('Profile manually refreshed');
                        alert('Profile data refreshed!');
                      });
                    }}
                    className="flex items-center text-gray-600 text-sm font-medium action-button"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-4 h-4 mr-1">
                      <path d="M21.5 2v6h-6M2.5 22v-6h6M2 11.5a10 10 0 0 1 18.8-4.3M22 12.5a10 10 0 0 1-18.8 4.2"/>
                    </svg>
                    Refresh
                  </button>
                )}
                <button
                  onClick={() => {
                    // If in edit mode, save changes
                    if (editMode) {
                      handleSave();
                    } else {
                      // If switching to edit mode, refresh the profile data first
                      fetchAdminProfile().then(() => {
                        console.log('Profile refreshed before editing');
                      });
                      setEditMode(true);
                    }
                  }}
                  className="flex items-center text-indigo-700 text-sm font-medium action-button"
                >
                  {editMode ? (
                    <>
                      <Save className="w-4 h-4 mr-1" />
                      Save Changes
                    </>
                  ) : (
                    <>
                      <Edit className="w-4 h-4 mr-1" />
                      Edit Profile
                    </>
                  )}
                </button>
              </div>
            </div>
            <div>
  <label className="block text-sm text-gray-500 mb-2">Profile Picture</label>
  <div className="flex items-center space-x-4">
    <img
      src={adminProfile && adminProfile.picture_url ? getProfilePicturePath(adminProfile.picture_url) : avatar}
      alt="Profile"
      className="w-24 h-24 rounded-full object-cover border-2 border-indigo-100 profile-image"
      onError={(e) => { 
        console.log('Image failed to load, using default avatar');
        e.target.src = avatar; 
      }}
    />
    {editMode && (
      <div className="flex flex-col space-y-2">
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleProfilePictureUpload}
          accept="image/jpeg,image/png,image/gif"
          className="hidden"
        />
        <button
          onClick={triggerFileInput}
          className="px-3 py-2 bg-indigo-50 text-indigo-700 rounded-md flex items-center text-sm font-medium action-button"
        >
          <Upload className="w-4 h-4 mr-1" />
          Upload New Image
        </button>
        {adminProfile && adminProfile.picture_url && !adminProfile.picture_url.includes('default.png') && (
          <button
            onClick={handleDeleteProfilePicture}
            className="px-3 py-2 bg-red-50 text-red-600 rounded-md flex items-center text-sm font-medium action-button"
          >
            <Trash2 className="w-4 h-4 mr-1" />
            Remove Picture
          </button>
        )}
      </div>
    )}
  </div>
  {uploadStatus && (
    <div className={`mt-2 text-sm ${uploadStatus === 'success' ? 'text-green-600' : 'text-red-600'} flex items-center`}>
      {uploadStatus === 'success' ? (
        <CheckCircle className="w-4 h-4 mr-1" />
      ) : (
        <XCircle className="w-4 h-4 mr-1" />
      )}
      {uploadMessage}
    </div>
  )}
</div>
            <div className="grid grid-cols-2 gap-6">
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm text-gray-500 mb-1">Full Name</label>
                  {editMode ? (
                    <input
                      type="text"
                      value={userData.name}
                      onChange={(e) => setUserData({...userData, name: e.target.value})}
                      className="w-full p-2 border rounded-md focus-ring"
                    />
                  ) : (
                    <div className="text-gray-800 flex items-center">
                      <User className="w-4 h-4 mr-2 text-indigo-700" />
                      {userData.name}
                    </div>
                  )}
                </div>

                <div>
                  <label className="block text-sm text-gray-500 mb-1">Email Address</label>
                  {editMode ? (
                    <input
                      type="email"
                      value={userData.email}
                      onChange={(e) => setUserData({...userData, email: e.target.value})}
                      className="w-full p-2 border rounded-md focus-ring"
                    />
                  ) : (
                    <div className="text-gray-800 flex items-center">
                      <Mail className="w-4 h-4 mr-2 text-indigo-700" />
                      {userData.email}
                    </div>
                  )}
                </div>

                <div>
                  <label className="block text-sm text-gray-500 mb-1">Phone Number</label>
                  {editMode ? (
                    <input
                      type="tel"
                      value={userData.phone}
                      onChange={(e) => setUserData({...userData, phone: e.target.value})}
                      className="w-full p-2 border rounded-md focus-ring"
                    />
                  ) : (
                    <div className="text-gray-800 flex items-center">
                      <Phone className="w-4 h-4 mr-2 text-indigo-700" />
                      {userData.phone}
                    </div>
                  )}
                </div>

                <div>
                  <label className="block text-sm text-gray-500 mb-1">Position</label>
                  {editMode ? (
                    <input
                      type="text"
                      value={userData.position}
                      onChange={(e) => setUserData({...userData, position: e.target.value})}
                      className="w-full p-2 border rounded-md focus-ring"
                    />
                  ) : (
                    <div className="text-gray-800">
                      {userData.position}
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm text-gray-500 mb-1">Department</label>
                  {/* Department is always displayed as read-only, even in edit mode */}
                  <div className="text-gray-800 flex items-center">
                    <Building className="w-4 h-4 mr-2 text-indigo-700" />
                    {userData.department}
                  </div>
                </div>

                <div>
                  <label className="block text-sm text-gray-500 mb-1">Location</label>
                  {editMode ? (
                    <input
                      type="text"
                      value={userData.location}
                      onChange={(e) => setUserData({...userData, location: e.target.value})}
                      className="w-full p-2 border rounded-md focus-ring"
                    />
                  ) : (
                    <div className="text-gray-800 flex items-center">
                      <Globe className="w-4 h-4 mr-2 text-indigo-700" />
                      {userData.location}
                    </div>
                  )}
                </div>


                <div>
                  <label className="block text-sm text-gray-500 mb-1">Last Login</label>
                  <div className="text-gray-800">
                    {userData.lastLogin}
                  </div>
                </div>
              </div>
            </div>


          </div>
        );

      case 'security':
        return (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">Security Settings</h2>
              {editMode && (
                <button
                  onClick={handleSave}
                  className="flex items-center text-indigo-700 text-sm font-medium action-button"
                >
                  <Save className="w-4 h-4 mr-1" />
                  Save Changes
                </button>
              )}
            </div>

            <div className="space-y-4">
              <div className="p-4 bg-white border border-gray-200 rounded-md hover-card">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="font-medium flex items-center">
                      <Key className="w-4 h-4 mr-2 text-indigo-700" />
                      Change Password
                    </h3>
                    <p className="text-sm text-gray-500 mt-1">Update your password regularly to maintain security</p>
                  </div>
                  <button className="px-3 py-2 bg-indigo-50 text-indigo-700 rounded-md text-sm font-medium action-button">
                    Update Password
                  </button>
                </div>
              </div>

              <div className="p-4 bg-white border border-gray-200 rounded-md hover-card">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="font-medium flex items-center">
                      <Shield className="w-4 h-4 mr-2 text-indigo-700" />
                      Two-Factor Authentication
                    </h3>
                    <p className="text-sm text-gray-500 mt-1">Add an extra layer of security to your account</p>
                  </div>
                  <div className="flex items-center">
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={userData.twoFactorEnabled}
                        onChange={() => editMode && setUserData({...userData, twoFactorEnabled: !userData.twoFactorEnabled})}
                        className="sr-only peer"
                        disabled={!editMode}
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600 switch-track"></div>
                    </label>
                  </div>
                </div>
              </div>

              <div className="p-4 bg-white border border-gray-200 rounded-md hover-card">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="font-medium">API Access</h3>
                    <p className="text-sm text-gray-500 mt-1">Manage your API keys and access permissions</p>
                  </div>
                  <div className="flex items-center">
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={userData.apiAccess}
                        onChange={() => editMode && setUserData({...userData, apiAccess: !userData.apiAccess})}
                        className="sr-only peer"
                        disabled={!editMode}
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600 switch-track"></div>
                    </label>
                  </div>
                </div>
                {userData.apiAccess && (
                  <div className="mt-4 p-3 bg-gray-50 rounded-md text-sm">
                    <div className="font-medium text-gray-700">Your API Key:</div>
                    <div className="mt-1 font-mono text-xs bg-gray-100 p-2 rounded">
                      dsh_29f8a3b5c7e6d4a1b8c9d0e2f3a4b5c6d7e8f9a0
                    </div>
                    <div className="mt-2 text-xs text-gray-500">Last used: March 6, 2025 at 14:23 UTC</div>
                  </div>
                )}
              </div>

              <div className="p-4 bg-white border border-gray-200 rounded-md hover-card">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="font-medium flex items-center">
                      <Clock className="w-4 h-4 mr-2 text-indigo-700" />
                      Session Management
                    </h3>
                    <p className="text-sm text-gray-500 mt-1">Manage active sessions and logout options</p>
                  </div>
                  <button className="px-3 py-2 bg-red-50 text-red-600 rounded-md text-sm font-medium action-button flex items-center">
                    <LogOut className="w-4 h-4 mr-1" />
                    Logout All Devices
                  </button>
                </div>
                <div className="mt-4 space-y-2">
                  <div className="p-3 bg-gray-50 rounded-md flex justify-between items-center">
                    <div>
                      <div className="font-medium text-sm">Chrome / Windows</div>
                      <div className="text-xs text-gray-500">Frankfurt, Germany • Current Session</div>
                    </div>
                    <span className="text-green-500 text-xs font-medium px-2 py-1 bg-green-50 rounded-full">Active</span>
                  </div>
                  <div className="p-3 bg-gray-50 rounded-md flex justify-between items-center">
                    <div>
                      <div className="font-medium text-sm">Mobile App / Android</div>
                      <div className="text-xs text-gray-500">Frankfurt, Germany • Last active: 3 hours ago</div>
                    </div>
                    <button className="text-gray-500 hover:text-red-500">
                      <LogOut className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>

              <div className="p-4 bg-white border border-gray-200 rounded-md hover-card">
                <div>
                  <h3 className="font-medium">Security Level</h3>
                  <p className="text-sm text-gray-500 mt-1">Current security clearance: <span className="font-medium text-indigo-700">{userData.securityLevel}</span></p>
                </div>
              </div>



            </div>
          </div>
        );

      case 'notifications':
        return (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">Notification Preferences</h2>
              {editMode && (
                <button
                  onClick={handleSave}
                  className="flex items-center text-indigo-700 text-sm font-medium action-button"
                >
                  <Save className="w-4 h-4 mr-1" />
                  Save Changes
                </button>
              )}
            </div>

            <div className="space-y-4">
              <div className="p-4 bg-white border border-gray-200 rounded-md hover-card">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="font-medium flex items-center">
                      <Mail className="w-4 h-4 mr-2 text-indigo-700" />
                      Email Notifications
                    </h3>
                    <p className="text-sm text-gray-500 mt-1">Receive notifications via email</p>
                  </div>
                  <div className="flex items-center">
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={userData.notifications.email}
                        onChange={() => editMode && setUserData({
                          ...userData,
                          notifications: {
                            ...userData.notifications,
                            email: !userData.notifications.email
                          }
                        })}
                        className="sr-only peer"
                        disabled={!editMode}
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600 switch-track"></div>
                    </label>
                  </div>
                </div>
              </div>

              <div className="p-4 bg-white border border-gray-200 rounded-md hover-card">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="font-medium flex items-center">
                      <Send className="w-4 h-4 mr-2 text-indigo-700" />
                      Telegram Notifications
                    </h3>
                    <p className="text-sm text-gray-500 mt-1">Receive notifications via Telegram</p>
                  </div>
                  <div className="flex items-center">
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={userData.notifications.telegram}
                        onChange={() => editMode && setUserData({
                          ...userData,
                          notifications: {
                            ...userData.notifications,
                            telegram: !userData.notifications.telegram
                          }
                        })}
                        className="sr-only peer"
                        disabled={!editMode}
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600 switch-track"></div>
                    </label>
                  </div>
                </div>
                {userData.notifications.telegram && (
                  <div className="mt-4 p-3 bg-gray-50 rounded-md text-sm">
                    <div className="font-medium text-gray-700">Telegram Username:</div>
                    {editMode ? (
                      <input
                        type="text"
                        value={userData.telegramUsername}
                        onChange={(e) => setUserData({...userData, telegramUsername: e.target.value})}
                        className="mt-1 w-full p-2 border rounded-md focus-ring"
                        placeholder="@username"
                      />
                    ) : (
                      <div className="mt-1 text-gray-600">{userData.telegramUsername}</div>
                    )}
                    <div className="mt-2 text-xs text-gray-500">You'll receive a verification message on Telegram</div>
                  </div>
                )}
              </div>

              <div className="p-4 bg-white border border-gray-200 rounded-md hover-card">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="font-medium flex items-center">
                      <Bell className="w-4 h-4 mr-2 text-indigo-700" />
                      System Notifications
                    </h3>
                    <p className="text-sm text-gray-500 mt-1">Receive in-app notifications</p>
                  </div>
                  <div className="flex items-center">
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={userData.notifications.system}
                        onChange={() => editMode && setUserData({
                          ...userData,
                          notifications: {
                            ...userData.notifications,
                            system: !userData.notifications.system
                          }
                        })}
                        className="sr-only peer"
                        disabled={!editMode}
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600 switch-track"></div>
                    </label>
                  </div>
                </div>
              </div>

              <div className="p-4 bg-white border border-gray-200 rounded-md hover-card">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="font-medium">Notification Types</h3>
                    <p className="text-sm text-gray-500 mt-1">Select which events trigger notifications</p>
                  </div>
                </div>
                <div className="mt-4 space-y-3">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="security-notifications"
                      checked={true}
                      className="h-4 w-4 text-indigo-600 rounded"
                      disabled={!editMode}
                    />
                    <label htmlFor="security-notifications" className="ml-2 text-sm text-gray-700">Security Alerts</label>
                  </div>
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="task-notifications"
                      checked={true}
                      className="h-4 w-4 text-indigo-600 rounded"
                      disabled={!editMode}
                    />
                    <label htmlFor="task-notifications" className="ml-2 text-sm text-gray-700">Task Updates</label>
                  </div>
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="system-notifications"
                      checked={true}
                      className="h-4 w-4 text-indigo-600 rounded"
                      disabled={!editMode}
                    />
                    <label htmlFor="system-notifications" className="ml-2 text-sm text-gray-700">System Maintenance</label>
                  </div>
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="team-notifications"
                      checked={false}
                      className="h-4 w-4 text-indigo-600 rounded"
                      disabled={!editMode}
                    />
                    <label htmlFor="team-notifications" className="ml-2 text-sm text-gray-700">Team Messages</label>
                  </div>
                </div>
              </div>
            </div>

            <div className="p-4 bg-yellow-50 border border-yellow-100 rounded-md">
              <div className="flex items-start">
                <div className="mr-3 mt-0.5 text-yellow-500">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" strokeWidth="2" />
                    <path d="M12 8V12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
                    <circle cx="12" cy="16" r="1" fill="currentColor" />
                  </svg>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-yellow-800">Important Notice</h4>
                  <p className="text-xs text-yellow-700 mt-1">
                    Critical system notifications will always be sent regardless of your preferences to ensure operational awareness.
                  </p>
                </div>
              </div>
            </div>
          </div>
        );

      case 'preferences':
        return (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">User Preferences</h2>
              {editMode && (
                <button
                  onClick={handleSave}
                  className="flex items-center text-indigo-700 text-sm font-medium action-button"
                >
                  <Save className="w-4 h-4 mr-1" />
                  Save Changes
                </button>
              )}
            </div>

            <div className="space-y-4">
              <div className="p-4 bg-white border border-gray-200 rounded-md hover-card">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="font-medium flex items-center">
                      <Languages className="w-4 h-4 mr-2 text-indigo-700" />
                      Language
                    </h3>
                    <p className="text-sm text-gray-500 mt-1">Select your preferred language</p>
                  </div>
                  <div className="flex items-center">
                    {editMode ? (
                      <select
                        value={userData.preferredLanguage}
                        onChange={(e) => setUserData({...userData, preferredLanguage: e.target.value})}
                        className="p-2 border rounded-md focus-ring"
                      >
                        <option value="English">English</option>
                        <option value="German">German</option>
                        <option value="French">French</option>
                        <option value="Spanish">Spanish</option>
                      </select>
                    ) : (
                      <span className="text-gray-700">{userData.preferredLanguage}</span>
                    )}
                  </div>
                </div>
              </div>

              <div className="p-4 bg-white border border-gray-200 rounded-md hover-card">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="font-medium flex items-center">
                      <Clock className="w-4 h-4 mr-2 text-indigo-700" />
                      Date & Time Format
                    </h3>
                    <p className="text-sm text-gray-500 mt-1">Configure how dates and times are displayed</p>
                  </div>
                </div>
                <div className="mt-4 grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm text-gray-500 mb-1">Date Format</label>
                    {editMode ? (
                      <select
                        value={userData.dateFormat}
                        onChange={(e) => setUserData({...userData, dateFormat: e.target.value})}
                        className="w-full p-2 border rounded-md focus-ring"
                      >
                        <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                        <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                        <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                      </select>
                    ) : (
                      <div className="text-gray-700">{userData.dateFormat}</div>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm text-gray-500 mb-1">Time Format</label>
                    {editMode ? (
                      <select
                        value={userData.timeFormat}
                        onChange={(e) => setUserData({...userData, timeFormat: e.target.value})}
                        className="w-full p-2 border rounded-md focus-ring"
                      >
                        <option value="12h">12-hour (AM/PM)</option>
                        <option value="24h">24-hour</option>
                      </select>
                    ) : (
                      <div className="text-gray-700">{userData.timeFormat === '24h' ? '24-hour' : '12-hour (AM/PM)'}</div>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm text-gray-500 mb-1">Timezone</label>
                    {editMode ? (
                      <select
                        value={userData.timezone}
                        onChange={(e) => setUserData({...userData, timezone: e.target.value})}
                        className="w-full p-2 border rounded-md focus-ring"
                      >
                        <option value="Europe/Berlin">Europe/Berlin</option>
                        <option value="Europe/London">Europe/London</option>
                        <option value="America_York">America_York</option>
                        <option value="Asia/Tokyo">Asia/Tokyo</option>
                      </select>
                    ) : (
                      <div className="text-gray-700">{userData.timezone}</div>
                    )}
                  </div>
                </div>
              </div>

              <div className="p-4 bg-white border border-gray-200 rounded-md hover-card">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="font-medium flex items-center">
                      {darkMode ? (
                        <Moon className="w-4 h-4 mr-2 text-indigo-700" />
                      ) : (
                        <Sun className="w-4 h-4 mr-2 text-indigo-700" />
                      )}
                      Theme Settings
                    </h3>
                    <p className="text-sm text-gray-500 mt-1">Choose between light and dark mode</p>
                  </div>
                  <div className="flex items-center">
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={darkMode}
                        onChange={() => setDarkMode(!darkMode)}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600 switch-track"></div>
                      <span className="ml-3 text-sm font-medium text-gray-700">
                        {darkMode ? 'Dark Mode' : 'Light Mode'}
                      </span>
                    </label>
                  </div>
                </div>
              </div>

              <div className="p-4 bg-white border border-gray-200 rounded-md hover-card">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium">Data Export</h3>
                    <p className="text-sm text-gray-500 mt-1">Download your account data</p>
                  </div>
                  <button className="px-3 py-2 bg-indigo-50 text-indigo-700 rounded-md text-sm font-medium action-button flex items-center">
                    <Download className="w-4 h-4 mr-1" />
                    Export Data
                  </button>
                </div>
              </div>
            </div>
          </div>
        );

      case 'integrations':
        return (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">Third-Party Integrations</h2>
              {editMode && (
                <button
                  onClick={handleSave}
                  className="flex items-center text-indigo-700 text-sm font-medium action-button"
                >
                  <Save className="w-4 h-4 mr-1" />
                  Save Changes
                </button>
              )}
            </div>

            <div className="space-y-4">
              <div className="p-4 bg-white border border-gray-200 rounded-md hover-card">
                <div className="flex justify-between items-center">
                  <div className="flex items-center">
                    <Github className="w-6 h-6 mr-3 text-gray-700" />
                    <div>
                      <h3 className="font-medium">GitHub</h3>
                      <p className="text-sm text-gray-500 mt-1">Connect your GitHub account for seamless integration</p>
                    </div>
                  </div>
                  <div>
                    {userData.integrations.github ? (
                      <div className="flex gap-3">
                        <span className="text-green-500 text-xs font-medium px-2 py-1 bg-green-50 rounded-full">Connected</span>
                        {editMode && (
                          <button className="text-xs text-red-600 underline">Disconnect</button>
                        )}
                      </div>
                    ) : (
                      <button className="px-3 py-2 bg-gray-100 text-gray-700 rounded-md text-sm font-medium action-button flex items-center">
                        Connect
                      </button>
                    )}
                  </div>
                </div>
              </div>

              <div className="p-4 bg-white border border-gray-200 rounded-md hover-card">
                <div className="flex justify-between items-center">
                  <div className="flex items-center">
                    <Slack className="w-6 h-6 mr-3 text-gray-700" />
                    <div>
                      <h3 className="font-medium">Slack</h3>
                      <p className="text-sm text-gray-500 mt-1">Get network alerts directly in your Slack channels</p>
                    </div>
                  </div>
                  <div>
                    {userData.integrations.slack ? (
                      <div className="flex gap-3">
                        <span className="text-green-500 text-xs font-medium px-2 py-1 bg-green-50 rounded-full">Connected</span>
                        {editMode && (
                          <button className="text-xs text-red-600 underline">Disconnect</button>
                        )}
                      </div>
                    ) : (
                      <button className="px-3 py-2 bg-gray-100 text-gray-700 rounded-md text-sm font-medium action-button flex items-center">
                        Connect
                      </button>
                    )}
                  </div>
                </div>
              </div>

              <div className="p-4 bg-white border border-gray-200 rounded-md hover-card">
                <div className="flex justify-between items-center">
                  <div className="flex items-center">
                    <Cpu className="w-6 h-6 mr-3 text-gray-700" />
                    <div>
                      <h3 className="font-medium">JIRA</h3>
                      <p className="text-sm text-gray-500 mt-1">Link your network tasks with JIRA tickets</p>
                    </div>
                  </div>
                  <div>
                    {userData.integrations.jira ? (
                      <div className="flex gap-3">
                        <span className="text-green-500 text-xs font-medium px-2 py-1 bg-green-50 rounded-full">Connected</span>
                        {editMode && (
                          <button className="text-xs text-red-600 underline">Disconnect</button>
                        )}
                      </div>
                    ) : (
                      <button className="px-3 py-2 bg-gray-100 text-gray-700 rounded-md text-sm font-medium action-button flex items-center">
                        Connect
                      </button>
                    )}
                  </div>
                </div>
              </div>

              <div className="p-4 bg-white border border-gray-200 rounded-md hover-card">
                <div className="flex justify-between items-center">
                  <div className="flex items-center">
                    <Send className="w-6 h-6 mr-3 text-gray-700" />
                    <div>
                      <h3 className="font-medium">Telegram</h3>
                      <p className="text-sm text-gray-500 mt-1">Connect your Telegram for notifications</p>
                    </div>
                  </div>
                  <div>
                    {userData.notifications.telegram ? (
                      <div className="flex gap-3">
                        <span className="text-green-500 text-xs font-medium px-2 py-1 bg-green-50 rounded-full">Connected</span>
                        {editMode && (
                          <button className="text-xs text-red-600 underline">Disconnect</button>
                        )}
                      </div>
                    ) : (
                      <button className="px-3 py-2 bg-gray-100 text-gray-700 rounded-md text-sm font-medium action-button flex items-center">
                        Connect
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 'activity':
        return (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">Account Activity</h2>
              <button className="px-3 py-2 bg-indigo-50 text-indigo-700 rounded-md text-sm font-medium action-button flex items-center">
                <Download className="w-4 h-4 mr-1" />
                Export Log
              </button>
            </div>

            <div className="bg-white border border-gray-200 rounded-md">
              <div className="flex items-center justify-between p-4 border-b">
                <h3 className="font-medium">Recent Activity</h3>
                <span className="text-sm text-gray-500">Showing last 30 days</span>
              </div>

              <div className="divide-y">
                {userData.recentActivity.map((activity, index) => (
                  <div key={index} className="p-4 hover:bg-gray-50">
                    <div className="flex items-start">
                      <div className="bg-indigo-100 p-2 rounded-full mr-3">
                        <Activity className="w-4 h-4 text-indigo-700" />
                      </div>
                      <div className="flex-grow">
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-800">{activity.action}</span>
                          <span className="text-sm text-gray-500">{activity.timestamp}</span>
                        </div>
                        <div className="text-sm text-gray-600 mt-1">
                          {activity.location} • {activity.device}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="p-4 border-t text-center">
                <button className="text-indigo-700 text-sm font-medium">
                  View All Activity
                </button>
              </div>
            </div>

            <div className="p-4 bg-yellow-50 border border-yellow-100 rounded-md">
              <div className="flex items-start">
                <div className="mr-3 mt-0.5 text-yellow-500">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" strokeWidth="2" />
                    <path d="M12 8V12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
                    <circle cx="12" cy="16" r="1" fill="currentColor" />
                  </svg>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-yellow-800">Security Notice</h4>
                  <p className="text-xs text-yellow-700 mt-1">
                    All login attempts, both successful and unsuccessful, are logged for security purposes.
                    If you notice any suspicious activity, please change your password immediately.
                  </p>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <Sidebar collapsed={sidebarCollapsed} activeMenu="My Account" navigateTo={navigateTo} />

      {/* Main Content */}
      <div className="flex-1 flex flex-col bg-gray-100">
        {/* Top Menu */}
        <TopMenu collapsed={sidebarCollapsed} toggleSidebar={toggleSidebar} />

        {/* Account Content */}
        <div className="p-6 space-y-6">
          <div className="flex items-center space-x-4">
            <h1 className="text-2xl font-bold text-gray-800">Account Management</h1>
            {editMode && (
              <button
                onClick={handleSave}
                className="bg-indigo-700 hover:bg-indigo-800 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 primary-button"
              >
                <Save className="w-4 h-4 mr-1 inline-block" />
                Save All Changes
              </button>
            )}
          </div>

          {/* Account Tabs */}
          <div className="bg-white shadow-sm border border-gray-200 dashboard-card">
            <div className="border-b">
              <div className="flex overflow-x-auto">
                <button
                  className={`px-6 py-3 text-sm font-medium tab-item ${activeTab === 'profile' ? 'text-indigo-700 border-b-2 border-indigo-700' : 'text-gray-500'}`}
                  onClick={() => setActiveTab('profile')}
                >
                  Profile
                </button>
                <button
                  className={`px-6 py-3 text-sm font-medium tab-item ${activeTab === 'security' ? 'text-indigo-700 border-b-2 border-indigo-700' : 'text-gray-500'}`}
                  onClick={() => setActiveTab('security')}
                >
                  Security
                </button>
                <button
                  className={`px-6 py-3 text-sm font-medium tab-item ${activeTab === 'notifications' ? 'text-indigo-700 border-b-2 border-indigo-700' : 'text-gray-500'}`}
                  onClick={() => setActiveTab('notifications')}
                >
                  Notifications
                </button>
                <button
                  className={`px-6 py-3 text-sm font-medium tab-item ${activeTab === 'preferences' ? 'text-indigo-700 border-b-2 border-indigo-700' : 'text-gray-500'}`}
                  onClick={() => setActiveTab('preferences')}
                >
                  Preferences
                </button>
                <button
                  className={`px-6 py-3 text-sm font-medium tab-item ${activeTab === 'integrations' ? 'text-indigo-700 border-b-2 border-indigo-700' : 'text-gray-500'}`}
                  onClick={() => setActiveTab('integrations')}
                >
                  Integrations
                </button>
                <button
                  className={`px-6 py-3 text-sm font-medium tab-item ${activeTab === 'activity' ? 'text-indigo-700 border-b-2 border-indigo-700' : 'text-gray-500'}`}
                  onClick={() => setActiveTab('activity')}
                >
                  Activity
                </button>
              </div>
            </div>

            <div className="p-6">
              {renderTabContent()}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AccountPage;