import React, { useState, useEffect } from 'react';
import {
  XCircle,
  Send,
  Printer,
  Download,
  Edit,
  CheckCircle,
  Plus,
  Calendar,
  Mail,
  Copy,
  ExternalLink,
  AlertCircle,
  Clock,
  File,
  Search,
  FileText
} from 'lucide-react';

// DatePicker component (unchanged from original)
const DatePicker = ({ value, onChange, name, placeholder = "DD/MM/YYYY" }) => {
  // Format date as DD/MM/YYYY for display
  const formatDisplayDate = (dateString) => {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return '';
      const day = String(date.getDate()).padStart(2, '0');
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const year = date.getFullYear();
      return `${day}/${month}/${year}`;
    } catch (e) {
      console.error("Error formatting date:", e);
      return '';
    }
  };

  // Format date as YYYY-MM-DD for the hidden date input
  const formatDateValue = (dateString) => {
    if (!dateString) return '';
    try {
      // Check if already in DD/MM/YYYY format
      if (dateString.includes('/')) {
        const parts = dateString.split('/');
        if (parts.length === 3) {
          const day = parseInt(parts[0], 10);
          const month = parseInt(parts[1], 10) - 1;
          const year = parseInt(parts[2], 10);
          const date = new Date(year, month, day);
          if (!isNaN(date.getTime())) {
            return date.toISOString().split('T')[0];
          }
        }
        return '';
      }

      // Already in date object or YYYY-MM-DD format
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return '';
      return date.toISOString().split('T')[0];
    } catch (e) {
      console.error("Error formatting date for input:", e);
      return '';
    }
  };

  // Parse DD/MM/YYYY to date object or YYYY-MM-DD
  const parseUserInput = (input) => {
    if (!input) return '';

    // Check if it's in DD/MM/YYYY format
    const parts = input.split('/');
    if (parts.length === 3) {
      const day = parseInt(parts[0], 10);
      const month = parseInt(parts[1], 10) - 1;
      const year = parseInt(parts[2], 10);

      // Create and validate the date
      const date = new Date(year, month, day);
      if (!isNaN(date.getTime())) {
        return date.toISOString().split('T')[0];
      }
    }

    return input; // Return as is if not in expected format
  };

  // Handle text input changes (DD/MM/YYYY format)
  const handleTextChange = (e) => {
    const userInput = e.target.value;
    // If input is empty or incomplete, just store as is
    if (!userInput || userInput.length < 10) {
      onChange({ target: { name, value: userInput } });
      return;
    }

    // Try to parse the input as DD/MM/YYYY
    const parsedDate = parseUserInput(userInput);
    onChange({ target: { name, value: parsedDate } });
  };

  const handleCalendarChange = (e) => {
    onChange(e);
  };

  return (
    <div className="relative flex">
      {/* Visible text input with DD/MM/YYYY format */}
      <div className="relative flex-grow">
        <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">
          <Calendar className="w-4 h-4" />
        </div>
        <input
          type="text"
          name={name}
          value={formatDisplayDate(value)}
          onChange={handleTextChange}
          className="w-full p-2 pl-10 bg-white border border-gray-300 rounded-md text-gray-700 focus:ring-indigo-500 focus:border-indigo-500"
          placeholder={placeholder}
        />
      </div>

      {/* Hidden date input that opens calendar when clicked */}
      <div className="ml-2">
        <label
          htmlFor={`calendar-${name}`}
          className="block p-2 bg-indigo-50 text-indigo-700 rounded-md cursor-pointer hover:bg-indigo-100"
        >
          <Calendar className="w-5 h-5" />
        </label>
        <input
          id={`calendar-${name}`}
          type="date"
          name={name}
          value={formatDateValue(value)}
          onChange={handleCalendarChange}
          className="sr-only" // Visually hidden but still functional
        />
      </div>
    </div>
  );
};

// Helper function to format dates
const formatDate = (dateString) => {
  if (!dateString) return '';
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '';
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  } catch (e) {
    console.error("Error formatting date:", e);
    return '';
  }
};

// Status badge renderer
const renderStatusBadge = (status, isProforma) => {
  if (isProforma) {
    return (
      <span className="px-2 py-1 rounded-full text-xs font-medium flex items-center w-fit bg-purple-100 text-purple-800">
        <FileText className="w-4 h-4 mr-1" />
        Proforma
      </span>
    );
  }

  // Original status badges logic
  const badgeClasses = {
    'Paid': 'bg-green-100 text-green-800',
    'Pending': 'bg-yellow-100 text-yellow-800',
    'Overdue': 'bg-red-100 text-red-800',
    'Disputed': 'bg-purple-100 text-purple-800',
    'Draft': 'bg-blue-100 text-blue-800'
  };

  const icons = {
    'Paid': <CheckCircle className="w-4 h-4 mr-1" />,
    'Pending': <Clock className="w-4 h-4 mr-1" />,
    'Overdue': <AlertCircle className="w-4 h-4 mr-1" />,
    'Disputed': <AlertCircle className="w-4 h-4 mr-1" />,
    'Draft': <File className="w-4 h-4 mr-1" />
  };

  return (
    <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center w-fit ${badgeClasses[status] || 'bg-gray-100 text-gray-800'}`}>
      {icons[status] || <AlertCircle className="w-4 h-4 mr-1" />}
      {status}
    </span>
  );
};

// Calculate due date status
const getDueDateStatus = (dueDate, status) => {
  if (status === 'Paid' || status === 'Draft') return null;

  const today = new Date();
  const due = new Date(dueDate);
  const diffDays = Math.ceil((due - today) / (1000 * 60 * 60 * 24));

  if (diffDays < 0) {
    return <span className="text-xs text-red-600">{Math.abs(diffDays)} days overdue</span>;
  } else if (diffDays <= 7) {
    return <span className="text-xs text-yellow-600">Due in {diffDays} days</span>;
  }

  return null;
};

/**
 * A flexible Invoice Modal component that can be used for viewing, creating or editing invoices
 *
 * @param {Object} props Component props
 * @param {boolean} props.isOpen Whether the modal is open
 * @param {string} props.mode The mode of the modal ('view', 'create', 'edit')
 * @param {Object} props.invoice The invoice data for view/edit modes
 * @param {Function} props.onClose Function to call when closing the modal
 * @param {Function} props.onSave Function to call when saving/creating an invoice
 * @param {Function} props.onUpdateStatus Function to call when updating invoice status
 * @param {Function} props.onSendInvoice Function to call when sending an invoice
 * @param {Function} props.onPrint Function to call when printing an invoice
 * @param {Function} props.onDownload Function to call when downloading an invoice
 * @param {Array} props.searchResults Search results for client selection (create mode)
 * @param {boolean} props.isSearching Whether a search is in progress
 * @param {Function} props.onClientSearch Function to search clients
 * @param {Array} props.statuses Available status options
 * @param {Array} props.paymentMethods Available payment method options
 * @param {Function} props.fetchVatRate Function to fetch VAT rate based on client country
 */
const InvoiceModal = ({
  isOpen,
  mode = 'view', // 'view', 'create', 'edit'
  invoice = null,
  onClose,
  onSave,
  onUpdateStatus,
  onSendInvoice,
  onPrint,
  onDownload,
  searchResults = [],
  isSearching = false,
  onClientSearch,
  statuses = ['Paid', 'Pending', 'Overdue', 'Disputed', 'Draft'],
  paymentMethods = ['Bank Transfer', 'Credit Card', 'PayPal', 'Cash', 'Check'],
  fetchVatRate = null
}) => {
  // State for the invoice form
  const [invoiceData, setInvoiceData] = useState({
    id: '',
    client: '',
    clientId: '',
    clientEmail: '',
    date: new Date().toISOString().split('T')[0],
    // Set default due date to tomorrow instead of 30 days from now
    dueDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    paymentDate: '',
    paymentMethod: 'Bank Transfer',
    status: 'Draft',
    notes: '',
    items: [{ description: '', quantity: 1, unitPrice: '', total: '' }],
    amount: '0.00',
    subtotal: '€0.00',
    tax: '€0.00',
    total: '€0.00',
    vatRate: 0.19, // Default VAT rate (will be updated based on client country)
  });

  // State for search interface (create mode)
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearchResults, setShowSearchResults] = useState(false);

  // Initialize form when invoice or mode changes
  useEffect(() => {
    if (mode === 'view' || mode === 'edit') {
      if (invoice) {
        // Add default empty array for items if not provided
        const items = invoice.items || [];

        // Handle dates
        let standardizedDate = invoice.date;
        let standardizedDueDate = invoice.dueDate;

        // If the date is in a different format, standardize it
        if (invoice.date && !invoice.date.includes('/')) {
          const dateObj = new Date(invoice.date);
          if (!isNaN(dateObj.getTime())) {
            standardizedDate = `${dateObj.getFullYear()}-${String(dateObj.getMonth() + 1).padStart(2, '0')}-${String(dateObj.getDate()).padStart(2, '0')}`;
          }
        }

        if (invoice.dueDate && !invoice.dueDate.includes('/')) {
          const dueDateObj = new Date(invoice.dueDate);
          if (!isNaN(dueDateObj.getTime())) {
            standardizedDueDate = `${dueDateObj.getFullYear()}-${String(dueDateObj.getMonth() + 1).padStart(2, '0')}-${String(dueDateObj.getDate()).padStart(2, '0')}`;
          }
        }

        // Set the invoice data
        setInvoiceData({
          ...invoiceData,
          ...invoice,
          date: standardizedDate,
          dueDate: standardizedDueDate,
          items: items.map(item => ({
            description: item.description,
            quantity: item.quantity,
            unitPrice: typeof item.unitPrice === 'string' && item.unitPrice.includes('€')
              ? item.unitPrice.replace('€', '').trim()
              : item.unitPrice,
            total: typeof item.total === 'string' && item.total.includes('€')
              ? item.total.replace('€', '').trim()
              : item.total
          })),
          vatRate: invoice.vatRate || 0.19 // Use existing rate or default to 19%
        });
      }
    } else {
      // Reset for create mode
      setInvoiceData({
        id: '',
        client: '',
        clientId: '',
        clientEmail: '',
        date: new Date().toISOString().split('T')[0],
        // Set default due date to tomorrow
        dueDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        paymentDate: '',
        paymentMethod: 'Bank Transfer',
        status: 'Draft',
        notes: '',
        items: [{ description: '', quantity: 1, unitPrice: '', total: '' }],
        amount: '0.00',
        subtotal: '€0.00',
        tax: '€0.00',
        total: '€0.00',
        vatRate: 0.19, // Default VAT rate
      });
      setSearchQuery('');
    }
  }, [invoice, mode, isOpen]);

  // Calculate totals when items change
  useEffect(() => {
    if (invoiceData.items && invoiceData.items.length > 0) {
      const subtotal = invoiceData.items.reduce(
        (sum, item) => sum + (parseFloat(item.total) || 0),
        0
      );
      // Use dynamic VAT rate instead of hardcoded 19%
      const tax = subtotal * invoiceData.vatRate;
      const total = subtotal + tax;

      setInvoiceData({
        ...invoiceData,
        amount: total.toFixed(2),
        subtotal: `€${subtotal.toFixed(2)}`,
        tax: `€${tax.toFixed(2)}`,
        total: `€${total.toFixed(2)}`
      });
    }
  }, [invoiceData.items, invoiceData.vatRate]);

  // Handle basic input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setInvoiceData({
      ...invoiceData,
      [name]: value
    });
  };

  // Handle item changes
  const handleItemChange = (index, field, value) => {
    const updatedItems = [...invoiceData.items];
    updatedItems[index][field] = value;

    // Auto-calculate total when quantity or unitPrice changes
    if (field === 'quantity' || field === 'unitPrice') {
      const quantity = parseFloat(updatedItems[index].quantity) || 0;
      const unitPrice = parseFloat(updatedItems[index].unitPrice) || 0;
      updatedItems[index].total = (quantity * unitPrice).toFixed(2);
    }

    setInvoiceData({
      ...invoiceData,
      items: updatedItems
    });
  };

  // Add a new item
  const handleAddItem = () => {
    setInvoiceData({
      ...invoiceData,
      items: [...invoiceData.items, { description: '', quantity: 1, unitPrice: '', total: '' }]
    });
  };

  // Remove an item
  const handleRemoveItem = (index) => {
    if (invoiceData.items.length === 1) return; // Keep at least one item

    const updatedItems = invoiceData.items.filter((_, i) => i !== index);

    setInvoiceData({
      ...invoiceData,
      items: updatedItems
    });
  };

  // Handle client search
  const handleSearchChange = (e) => {
    const query = e.target.value;
    setSearchQuery(query);

    if (onClientSearch) {
      onClientSearch(query);
    }
  };


// Handle client selection
const handleSelectClient = async (client) => {
  console.log("Selected client:", client);

  // Extract numeric ID if client ID contains a prefix like "CLI-"
  let clientId = client.id;
  if (typeof clientId === 'string' && clientId.includes('-')) {
    const numericId = clientId.split('-')[1];
    if (numericId && !isNaN(parseInt(numericId))) {
      clientId = parseInt(numericId);
    }
  }

  setInvoiceData({
    ...invoiceData,
    client: client.display_name || client.full_name || client.email,
    clientId: clientId, // Use the extracted numeric ID if possible
    clientEmail: client.email
  });

  setSearchQuery(client.display_name || client.full_name || client.email);
  setShowSearchResults(false);

  // Fetch and update VAT rate based on selected client's country
  if (fetchVatRate && client.country) {
    try {
      const vatRate = await fetchVatRate(client.country);
      // Update the VAT rate in the invoice data
      setInvoiceData(prevState => ({
        ...prevState,
        vatRate: vatRate || 0.19, // Default to 19% if no rate found
        client: client.display_name || client.full_name || client.email,
        clientId: clientId, // Ensure clientId is consistent
        clientEmail: client.email
      }));
    } catch (error) {
      console.error("Error fetching VAT rate:", error);
    }
  }
};

  // Handle save/create action
  const handleSave = () => {
    // Validate required fields
    if (mode === 'create' || mode === 'edit') {
      if ((mode === 'create' && !invoiceData.client) ||
          invoiceData.items.some(item => !item.description || !item.unitPrice)) {
        alert('Please fill in all required fields');
        return;
      }
    }

    // Ensure due date is set to tomorrow if not provided
    let dueDateToUse = invoiceData.dueDate;
    if (!dueDateToUse) {
      dueDateToUse = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0];
    }

    // Update the invoiceData with the final due date before saving
    const finalInvoiceData = {
      ...invoiceData,
      dueDate: dueDateToUse
    };

    if (onSave) {
      // Pass the complete invoice data to onSave
      // The backend should save each item in the items array as separate entries
      onSave(finalInvoiceData);
    }
  };

  // If modal is not open, don't render anything
  if (!isOpen) return null;

  // Render the appropriate modal content based on mode
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Modal Header */}
        <div className="p-6 border-b flex justify-between items-center sticky top-0 z-10 bg-white">
  <div className="flex items-center">
    <h2 className="text-xl font-bold text-gray-800">
      {mode === 'create' ? 'Create New Invoice' :
       mode === 'edit' ? `Edit ${invoiceData.isProforma ? 'Proforma Invoice' : 'Invoice'} ${invoiceData.id}` :
       invoiceData.isProforma ? `Proforma Invoice ${invoiceData.id}` :
       `Invoice ${invoiceData.id}`}
    </h2>
    {mode !== 'create' && !invoiceData.isProforma && (
      <div className="ml-4">
        {/* Only show status badge for regular invoices */}
        {renderStatusBadge(invoiceData.status, false)}
      </div>
    )}
  </div>
  <button
    onClick={onClose}
    className="text-gray-500 hover:text-gray-700"
  >
    <XCircle className="w-5 h-5" />
  </button>
</div>

        {/* Modal Body */}
        <div className="p-6 space-y-6">
          {/* Invoice Header - View Mode Only */}
          {mode === 'view' && (
  <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
    <div>
      <div className="text-sm text-gray-500">
        {invoiceData.isProforma ? 'Proforma Invoice Number' : 'Invoice Number'}
      </div>
      <div className="text-lg font-bold text-indigo-700">{invoiceData.id}</div>
    </div>
    <div className="mt-4 sm:mt-0 flex flex-wrap gap-3">
                <button
                  onClick={onSendInvoice ? () => onSendInvoice(invoiceData) : undefined}
                  className="px-3 py-2 bg-indigo-50 text-indigo-700 rounded-md flex items-center text-sm font-medium action-button"
                >
                  <Send className="w-4 h-4 mr-1" />
                  Send Invoice
                </button>
                <button
                  onClick={onPrint ? () => onPrint(invoiceData) : undefined}
                  className="px-3 py-2 bg-indigo-50 text-indigo-700 rounded-md flex items-center text-sm font-medium action-button"
                >
                  <Printer className="w-4 h-4 mr-1" />
                  Print
                </button>
                <button
                  onClick={onDownload ? () => onDownload(invoiceData) : undefined}
                  className="px-3 py-2 bg-indigo-50 text-indigo-700 rounded-md flex items-center text-sm font-medium action-button"
                >
                  <Download className="w-4 h-4 mr-1" />
                  Download PDF
                </button>
              </div>
            </div>
          )}

          {/* Create Mode - Client Search */}
          {mode === 'create' && (
            <div className="mb-6">
              <h3 className="text-lg font-medium text-gray-800 mb-4">Invoice Information</h3>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Client Autocomplete */}
                <div className="md:col-span-2 relative">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Client*</label>
                  <div className="flex items-center relative">
                    <input
                      type="text"
                      value={searchQuery}
                      onChange={handleSearchChange}
                      onClick={() => setShowSearchResults(true)}
                      onBlur={() => setTimeout(() => setShowSearchResults(false), 200)}
                      className="block w-full p-3 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                      placeholder="Search by email or name..."
                      required
                    />
                    <Search className="absolute right-3 text-gray-400" size={18} />
                  </div>

                  {invoiceData.clientId && (
                    <div className="mt-2 p-2 bg-indigo-50 rounded text-sm text-indigo-700 border border-indigo-100">
                      <div className="font-semibold">{invoiceData.client}</div>
                      <div className="text-xs text-indigo-600">Client ID: {invoiceData.clientId}</div>
                      {invoiceData.vatRate && (
                        <div className="text-xs text-indigo-600">
                          VAT Rate: {(invoiceData.vatRate * 100).toFixed(0)}%
                        </div>
                      )}
                    </div>
                  )}

                  {showSearchResults && (
                    <div className="absolute z-10 w-full mt-1 bg-white border rounded-md shadow-lg max-h-60 overflow-auto">
                      {searchResults.length > 0 ? (
                        searchResults.map(client => (
                          <div
                            key={client.id}
                            className="p-3 hover:bg-indigo-50 cursor-pointer border-b"
                            onClick={() => handleSelectClient(client)}
                          >
                            <div className="font-medium text-indigo-700">
                              {client.display_name || client.full_name || client.email}
                            </div>
                          </div>
                        ))
                      ) : (
                        searchQuery.length >= 2 && !isSearching ? (
                          <div className="p-3 text-center text-gray-500">
                            No users found matching "{searchQuery}"
                          </div>
                        ) : null
                      )}
                    </div>
                  )}
                </div>

                {/* Due date */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Due Date</label>
                  <DatePicker
                    name="dueDate"
                    value={invoiceData.dueDate}
                    onChange={(e) => setInvoiceData({...invoiceData, dueDate: e.target.value})}
                  />
                </div>
              </div>
            </div>
          )}

          {/* Invoice Details - Edit/View Modes */}
          {(mode === 'view' || mode === 'edit') && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 border-t border-b py-6">
              <div className="space-y-4">
                <div>
                  <div className="text-sm text-gray-500">Client</div>
                  <div className="font-medium">{invoiceData.client}</div>
                  <div className="text-sm text-gray-500 mt-1">{invoiceData.clientEmail}</div>
                  <div className="text-sm text-gray-500">Client ID: {invoiceData.clientId}</div>
                </div>

                <div>
                  <div className="text-sm text-gray-500">Payment Method</div>
                  {mode === 'edit' ? (
                    <select
                      value={invoiceData.paymentMethod || 'Bank Transfer'}
                      onChange={(e) => setInvoiceData({...invoiceData, paymentMethod: e.target.value})}
                      className="w-full p-2 bg-white border border-gray-300 rounded-md text-gray-700 focus:ring-indigo-500 focus:border-indigo-500"
                    >
                      {paymentMethods.map(method => (
                        <option key={method} value={method}>{method}</option>
                      ))}
                    </select>
                  ) : (
                    <div className="font-medium">{invoiceData.paymentMethod}</div>
                  )}
                </div>

        {/* Status Dropdown - Only show in edit mode */}
        {mode === 'edit' && (
                  <div>
                    <div className="text-sm font-medium text-gray-700 mb-1">Status</div>
                    <select
                      value={invoiceData.status}
                      onChange={(e) => setInvoiceData({...invoiceData, status: e.target.value})}
                      className="w-full p-2 bg-white border border-gray-300 rounded-md text-gray-700 focus:ring-indigo-500 focus:border-indigo-500"
                    >
                      {statuses.map(status => (
                        <option key={status} value={status}>{status}</option>
                      ))}
                    </select>
                  </div>
                )}
              </div>



              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm text-gray-500">Issue Date</div>
                    {mode === 'edit' ? (
                      <DatePicker
                        name="date"
                        value={invoiceData.date}
                        onChange={(e) => setInvoiceData({...invoiceData, date: e.target.value})}
                      />
                    ) : (
                      <div className="font-medium">{formatDate(invoiceData.date)}</div>
                    )}
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Due Date</div>
                    {mode === 'edit' ? (
                      <DatePicker
                        name="dueDate"
                        value={invoiceData.dueDate}
                        onChange={(e) => setInvoiceData({...invoiceData, dueDate: e.target.value})}
                      />
                    ) : (
                      <div className="font-medium">{formatDate(invoiceData.dueDate)}</div>
                    )}
                  </div>
                </div>

                {invoiceData.status === 'Paid' && (
                  <div>
                    <div className="text-sm text-gray-500">Payment Date</div>
                    <div className="font-medium">{formatDate(invoiceData.paymentDate)}</div>
                  </div>
                )}

                <div>
                  <div className="text-sm text-gray-500">Total Amount</div>
                  <div className="text-xl font-bold text-indigo-700">{`€${invoiceData.amount}`}</div>
                </div>
              </div>
            </div>
          )}

          {/* Invoice Items */}
          <div>
            <div className="flex justify-between items-center mb-3">
              <h3 className="text-lg font-bold">Invoice Items</h3>
              {(mode === 'create' || mode === 'edit') && (
                <button
                  type="button"
                  onClick={handleAddItem}
                  className="text-sm flex items-center bg-indigo-50 hover:bg-indigo-100 text-indigo-700 px-3 py-2 rounded-md transition-colors"
                >
                  <Plus className="w-4 h-4 mr-1" />
                  Add Item
                </button>
              )}
            </div>
            <div className="border rounded-md overflow-hidden">
              <table className="w-full">
                <thead className="bg-gray-50 text-left text-xs text-gray-500">
                  <tr>
                    <th className="py-3 px-4 font-medium">DESCRIPTION</th>
                    <th className="py-3 px-4 font-medium text-right w-24">QUANTITY</th>
                    <th className="py-3 px-4 font-medium text-right w-32">UNIT PRICE</th>
                    <th className="py-3 px-4 font-medium text-right">TOTAL</th>
                    {(mode === 'create' || mode === 'edit') && (
                      <th className="py-3 px-4 w-12"></th>
                    )}
                  </tr>
                </thead>
                <tbody className="divide-y">
                  {invoiceData.items.map((item, index) => (
                    <tr key={index} className="text-sm">
                      <td className="py-3 px-4">
                        {mode === 'view' ? (
                          <div className="text-gray-800">{item.description}</div>
                        ) : (
                          <textarea
                            value={item.description}
                            onChange={(e) => handleItemChange(index, 'description', e.target.value)}
                            className="w-full p-2 border border-gray-300 rounded text-sm resize-y bg-white"
                            placeholder="Item description"
                            rows="1"
                            required
                          />
                        )}
                      </td>
                      <td className="py-3 px-4 w-24">
                        {mode === 'view' ? (
                          <div className="text-gray-800 text-right">{item.quantity}</div>
                        ) : (
                          <input
                            type="number"
                            min="1"
                            max="999"
                            value={item.quantity}
                            onChange={(e) => {
                              const value = Math.min(parseInt(e.target.value) || 0, 999);
                              handleItemChange(index, 'quantity', value.toString());
                            }}
                            className="w-full p-2 border border-gray-300 rounded text-sm text-right bg-white"
                            required
                          />
                        )}
                      </td>
                      <td className="py-3 px-4 w-32">
                        {mode === 'view' ? (
                          <div className="text-gray-800 text-right">{`€${item.unitPrice}`}</div>
                        ) : (
                          <div className="relative">
                            <span className="absolute inset-y-0 left-0 flex items-center pl-2 text-gray-500">€</span>
                            <input
                              type="text"
                              value={item.unitPrice}
                              onChange={(e) => {
                                const value = e.target.value;
                                const numericValue = parseFloat(value.replace(/[^0-9.]/g, ''));
                                if (!isNaN(numericValue) && numericValue <= 999999) {
                                  handleItemChange(index, 'unitPrice', value);
                                } else if (isNaN(numericValue) || value === '') {
                                  handleItemChange(index, 'unitPrice', value);
                                }
                              }}
                              className="w-full p-2 pl-6 border border-gray-300 rounded text-sm text-right bg-white"
                              placeholder="0.00"
                              required
                            />
                          </div>
                        )}
                      </td>
                      <td className="py-3 px-4 font-medium text-right">{`€${item.total || '0.00'}`}</td>
                      {(mode === 'create' || mode === 'edit') && (
                        <td className="py-3 px-4 text-center">
                          {invoiceData.items.length > 1 && (
                            <button
                              type="button"
                              onClick={() => handleRemoveItem(index)}
                              className="text-gray-400 hover:text-red-600 p-1 rounded-full hover:bg-gray-100"
                            >
                              <XCircle className="w-4 h-4" />
                            </button>
                          )}
                        </td>
                      )}
                    </tr>
                  ))}
                </tbody>
                <tfoot className="bg-gray-50">
                  <tr>
                    <td colSpan="3" className="py-3 px-4 text-right font-medium">Subtotal</td>
                    <td className="py-3 px-4 text-right font-medium w-32">
                      {invoiceData.subtotal}
                    </td>
                    {(mode === 'create' || mode === 'edit') && <td></td>}
                  </tr>
                  <tr>
                    <td colSpan="3" className="py-3 px-4 text-right font-medium">
                      Tax ({(invoiceData.vatRate * 100).toFixed(0)}%)
                    </td>
                    <td className="py-3 px-4 text-right font-medium w-32">
                      {invoiceData.tax}
                    </td>
                    {(mode === 'create' || mode === 'edit') && <td></td>}
                  </tr>
                  <tr className="font-bold">
                    <td colSpan="3" className="py-3 px-4 text-right">Total</td>
                    <td className="py-3 px-4 text-right text-indigo-700 w-32">
                      {`€${invoiceData.amount}`}
                    </td>
                    {(mode === 'create' || mode === 'edit') && <td></td>}
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>

          {/* Notes */}
          <div>
            <h3 className="text-lg font-bold mb-2">Notes</h3>
            {mode === 'view' ? (
              <div className="bg-gray-50 rounded-md p-4">
                {invoiceData.notes ? (
                  <div className="text-sm text-gray-700">
                    {invoiceData.notes.split('\n').map((line, index) => (
                      <p key={index} className={index === 0 ? 'font-medium' : ''}>
                        {line}
                      </p>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-gray-700">No notes available.</p>
                )}
              </div>
            ) : (
              <div className="rounded-md border">
                <textarea
                  value={invoiceData.notes}
                  onChange={(e) => setInvoiceData({...invoiceData, notes: e.target.value})}
                  className="block w-full p-4 border-0 rounded-md focus:ring-2 focus:ring-indigo-300 focus:outline-none resize-y bg-white"
                  rows="4"
                  placeholder="Enter invoice notes (optional)"
                />
              </div>
            )}
          </div>

          {/* Invoice Actions */}
          <div className="sticky bottom-0 z-10 bg-white border-t p-6">
          <div className="flex flex-wrap gap-3 justify-end pt-4 border-t">
            {/* View Mode Actions */}


{/* View Mode Actions */}
{mode === 'view' && (
  <>
    {/* Draft status */}
    {invoiceData.status === 'Draft' && (
      <button
        onClick={() => {
          if (onUpdateStatus) {
            const confirmMessage = invoiceData.isProforma
              ? `Are you sure you want to finalize proforma invoice ${invoiceData.id}?`
              : `Are you sure you want to finalize invoice ${invoiceData.id}?`;

            if (!window.confirm(confirmMessage)) return;

            console.log(`Finalizing invoice ${invoiceData.id}`);

            // Update the invoice data status locally first for immediate feedback
            setInvoiceData({
              ...invoiceData,
              status: 'Pending'
            });

            // Call the parent component's handler
            onUpdateStatus(invoiceData.id, 'Pending');

            // Close the modal
            setTimeout(() => onClose(), 500);
          }
        }}
        className="px-4 py-2 bg-indigo-700 text-white rounded-md flex items-center text-sm hover:bg-indigo-800"
      >
        <Send className="w-4 h-4 mr-1" />
        Finalize & Send
      </button>
    )}

    {/* Pending status */}
    {invoiceData.status === 'Pending' && (
      <>
<button
  onClick={() => {
    if (onUpdateStatus) {
      // Special handling for proforma
      if (invoiceData.isProforma) {
        const confirmConvert = window.confirm(
          `Are you sure you want to mark proforma invoice ${invoiceData.id} as paid? ` +
          `This will convert it to a regular invoice.`
        );

        if (!confirmConvert) return;

        // First close the modal to prevent UI issues
        onClose();

        // Short delay before triggering the update
        setTimeout(() => {
          // Call the parent handler to process the conversion
          onUpdateStatus(invoiceData.id, 'Paid');
        }, 300);

        return;
      }

      // Standard invoice payment handling
      const confirmPay = window.confirm(
        `Are you sure you want to mark invoice ${invoiceData.id} as paid?`
      );

      if (!confirmPay) return;

      // Update local state first for immediate feedback
      setInvoiceData({
        ...invoiceData,
        status: 'Paid',
        paymentDate: new Date().toISOString().split('T')[0]
      });

      // Call the parent handler
      onUpdateStatus(invoiceData.id, 'Paid');

      // Close the modal with a slight delay
      setTimeout(() => onClose(), 300);
    }
  }}
  className="px-4 py-2 bg-green-600 text-white rounded-md flex items-center text-sm hover:bg-green-700"
>
  <CheckCircle className="w-4 h-4 mr-1" />
  Mark as Paid
</button>
        <button
          onClick={() => onSendInvoice && onSendInvoice(invoiceData)}
          className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 flex items-center text-sm hover:bg-gray-50"
        >
          <Mail className="w-4 h-4 mr-1" />
          Send Reminder
        </button>
      </>
    )}

    {/* Overdue status */}
    {invoiceData.status === 'Overdue' && (
      <>
        <button
          onClick={() => onSendInvoice && onSendInvoice(invoiceData, 'reminder')}
          className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 flex items-center text-sm hover:bg-gray-50"
        >
          <Mail className="w-4 h-4 mr-1" />
          Send Reminder
        </button>
        <button
          onClick={() => {
            if (onUpdateStatus) {
              const confirmMessage = `Are you sure you want to mark invoice ${invoiceData.id} as paid?`;
              if (!window.confirm(confirmMessage)) return;

              // Update local state first
              setInvoiceData({
                ...invoiceData,
                status: 'Paid',
                // Remove proforma flag if present
                isProforma: false
              });

              // Call the parent handler
              onUpdateStatus(invoiceData.id, 'Paid');

              // Close the modal
              setTimeout(() => onClose(), 500);
            }
          }}
          className="px-4 py-2 bg-green-600 text-white rounded-md flex items-center text-sm hover:bg-green-700"
        >
          <CheckCircle className="w-4 h-4 mr-1" />
          Mark as Paid
        </button>
      </>
    )}

    {/* Disputed status */}
    {invoiceData.status === 'Disputed' && (
      <button
        onClick={() => {
          if (onUpdateStatus) {
            // Update local state first
            setInvoiceData({
              ...invoiceData,
              status: 'Pending'
            });

            // Call the parent handler
            onUpdateStatus(invoiceData.id, 'Pending');

            // Close the modal
            setTimeout(() => onClose(), 500);
          }
        }}
        className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 flex items-center text-sm hover:bg-gray-50"
      >
        <Edit className="w-4 h-4 mr-1" />
        Resolve Dispute
      </button>
    )}

    {/* Paid status */}
    {invoiceData.status === 'Paid' && (
      <button
        className="px-4 py-2 bg-indigo-700 text-white rounded-md flex items-center text-sm hover:bg-indigo-800"
      >
        <ExternalLink className="w-4 h-4 mr-1" />
        View Receipt
      </button>
    )}
  </>
)}

            {/* Create/Edit Mode Actions */}
            {(mode === 'create' || mode === 'edit') && (
              <>
                <button
                  onClick={onClose}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 flex items-center text-sm hover:bg-gray-50"
                >
                  <XCircle className="w-4 h-4 mr-1" />
                  Cancel
                </button>
                <button
                  onClick={handleSave}
                  className="px-4 py-2 bg-indigo-700 text-white rounded-md flex items-center text-sm hover:bg-indigo-800"
                >
                  <CheckCircle className="w-4 h-4 mr-1" />
                  {mode === 'create' ? 'Create Invoice' : 'Save Changes'}
                </button>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
    </div>
  );
};

export default InvoiceModal;