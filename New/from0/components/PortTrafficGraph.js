import React, { useState, useEffect, useCallback } from 'react';
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Brush
} from 'recharts';
import {
  RefreshCw,
  AlertTriangle,
  Download,
  Clock,
  Calendar,
  ArrowUp,
  ArrowDown
} from 'lucide-react';
import { API_URL } from '../config';
const PortTrafficGraph = ({ port_id, days: initialDays = 1 }) => {
  // State
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [trafficData, setTrafficData] = useState([]);
  const [timeRange, setTimeRange] = useState(initialDays);
  const [refreshing, setRefreshing] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(null);
  const [visibleDomain, setVisibleDomain] = useState(null);
  
  // Colors
  const colors = {
    inbound: '#4f46e5', // indigo-600
    outbound: '#ef4444', // red-500
    inPackets: '#0ea5e9', // sky-500
    outPackets: '#f97316', // orange-500
    background: '#f9fafb', // gray-50
    grid: '#e5e7eb', // gray-200
  };

  // Reset zoom when time range changes
  useEffect(() => {
    setVisibleDomain(null);
  }, [timeRange]);

  // Fetch traffic data when component mounts or when dependencies change
  useEffect(() => {
    fetchTrafficData();
    
    // Set up auto-refresh for 1-day view only
    let refreshInterval;
    if (timeRange === 1) {
      refreshInterval = setInterval(() => {
        fetchTrafficData();
      }, 300000); // Refresh every 5 minutes
    }
    
    return () => {
      if (refreshInterval) clearInterval(refreshInterval);
    };
  }, [port_id, timeRange]);
  
  // Format time elapsed since last update
  const getLastUpdatedText = () => {
    if (!lastUpdated) return 'Never updated';
    
    const now = new Date();
    const diffInSeconds = Math.floor((now - lastUpdated) / 1000);
    
    if (diffInSeconds < 60) {
      return `${diffInSeconds}s ago`;
    } else if (diffInSeconds < 3600) {
      return `${Math.floor(diffInSeconds / 60)}m ago`;
    } else {
      return lastUpdated.toLocaleTimeString();
    }
  };

  // Fetch traffic data from the API
  const fetchTrafficData = async () => {
    if (!port_id) return;
    
    setLoading(true);
    setError(null);
    if (refreshing) setRefreshing(true);
    
    try {
      const token = localStorage.getItem('admin_token');
      
      // Request the selected time range
      const requestDays = timeRange;
      
      const params = {
        token,
        port_id: port_id,
        days: requestDays,
        interval: calculateIdealInterval(timeRange),
        timestamp: new Date().getTime() // Prevent caching
      };
      
      console.log(`Fetching port traffic data with range: ${requestDays} days`);
      
      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=get_port_traffic`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(params)
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
      
      const result = await response.json();
      
      if (result.success) {
        // Process and set the traffic data
        if (result.data && result.data.length > 0) {
          const processedData = result.data.map(point => ({
            ...point,
            formattedTime: formatTimestamp(point.timestamp),
            in_bps_formatted: formatBitRate(point.in_bps),
            out_bps_formatted: formatBitRate(point.out_bps),
            in_pps_formatted: formatNumber(point.in_pps),
            out_pps_formatted: formatNumber(point.out_pps),
            // Ensure all numeric fields are numbers
            in_bps: Number(point.in_bps) || 0,
            out_bps: Number(point.out_bps) || 0,
            in_pps: Number(point.in_pps) || 0,
            out_pps: Number(point.out_pps) || 0,
            // Convert timestamp to number for charting
            timestamp: new Date(point.timestamp).getTime(),
            // Keep original timestamp as Date object
            date: new Date(point.timestamp)
          }));
          
          // Fill in time series with zero values for missing data points
          const filledData = fillMissingDataPoints(processedData);
          
          setTrafficData(filledData);
          setLastUpdated(new Date());
          setVisibleDomain(null); // Reset zoom on new data
        } else {
          // Generate empty dataset with zero values for the full time range
          const emptyData = generateEmptyTimeSeries();
          setTrafficData(emptyData);
        }
      } else {
        throw new Error(result.error || 'Unknown error occurred');
      }
    } catch (error) {
      console.error('Error fetching traffic data:', error);
      setError(error.message);
      setTrafficData([]);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };
  
  // Generate a complete time series with evenly spaced time points
  const generateEmptyTimeSeries = () => {
    const now = new Date();
    const endTime = now.getTime();
    let startTime;
    
    // Calculate start time based on selected time range
    if (timeRange === 1) {
      startTime = new Date(now);
      startTime.setHours(startTime.getHours() - 24);
      startTime = startTime.getTime();
    } else if (timeRange === 7) {
      startTime = new Date(now);
      startTime.setDate(startTime.getDate() - 7);
      startTime = startTime.getTime();
    } else {
      startTime = new Date(now);
      startTime.setDate(startTime.getDate() - 30);
      startTime = startTime.getTime();
    }
    
    // Calculate interval in milliseconds based on time range
    const intervalMs = calculateIntervalInMs(timeRange);
    
    // Generate time points
    const timeSeries = [];
    for (let time = startTime; time <= endTime; time += intervalMs) {
      const point = {
        timestamp: time,
        date: new Date(time),
        formattedTime: formatTimestamp(time),
        in_bps: 0,
        out_bps: 0,
        in_pps: 0,
        out_pps: 0,
        in_errors: 0,
        out_errors: 0,
        in_bytes: 0,
        out_bytes: 0,
        in_bps_formatted: formatBitRate(0),
        out_bps_formatted: formatBitRate(0),
        in_pps_formatted: formatNumber(0),
        out_pps_formatted: formatNumber(0)
      };
      timeSeries.push(point);
    }
    
    return timeSeries;
  };
  
  // Calculate the time interval in milliseconds
  const calculateIntervalInMs = (days) => {
    // Convert minutes to milliseconds
    return calculateIdealInterval(days) * 60 * 1000;
  };
  
  // Fill in missing data points in the time series
  const fillMissingDataPoints = (data) => {
    if (!data || data.length === 0) {
      return generateEmptyTimeSeries();
    }
    
    // Sort the data by timestamp
    const sortedData = [...data].sort((a, b) => a.timestamp - b.timestamp);
    
    // Determine start and end time
    let startTime = sortedData[0].timestamp;
    let endTime = sortedData[sortedData.length - 1].timestamp;
    
    // If the data doesn't cover the full range, expand it
    const now = new Date();
    
    if (timeRange === 1) {
      const oneDayAgo = new Date(now);
      oneDayAgo.setHours(oneDayAgo.getHours() - 24);
      if (startTime > oneDayAgo.getTime()) {
        startTime = oneDayAgo.getTime();
      }
      if (endTime < now.getTime()) {
        endTime = now.getTime();
      }
    } else if (timeRange === 7) {
      const sevenDaysAgo = new Date(now);
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      if (startTime > sevenDaysAgo.getTime()) {
        startTime = sevenDaysAgo.getTime();
      }
      if (endTime < now.getTime()) {
        endTime = now.getTime();
      }
    } else {
      const thirtyDaysAgo = new Date(now);
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      if (startTime > thirtyDaysAgo.getTime()) {
        startTime = thirtyDaysAgo.getTime();
      }
      if (endTime < now.getTime()) {
        endTime = now.getTime();
      }
    }
    
    // Create a map of existing data points
    const dataMap = {};
    sortedData.forEach(point => {
      dataMap[point.timestamp] = point;
    });
    
    // Calculate interval in milliseconds
    const intervalMs = calculateIntervalInMs(timeRange);
    
    // Generate complete time series
    const filledData = [];
    for (let time = startTime; time <= endTime; time += intervalMs) {
      // Round the time to the nearest interval to match potential data points
      const roundedTime = Math.round(time / intervalMs) * intervalMs;
      
      if (dataMap[roundedTime]) {
        // Use existing data point
        filledData.push(dataMap[roundedTime]);
      } else {
        // Create zero-value data point
        const point = {
          timestamp: roundedTime,
          date: new Date(roundedTime),
          formattedTime: formatTimestamp(roundedTime),
          in_bps: 0,
          out_bps: 0,
          in_pps: 0,
          out_pps: 0,
          in_errors: 0,
          out_errors: 0,
          in_bytes: 0,
          out_bytes: 0,
          in_bps_formatted: formatBitRate(0),
          out_bps_formatted: formatBitRate(0),
          in_pps_formatted: formatNumber(0),
          out_pps_formatted: formatNumber(0)
        };
        filledData.push(point);
      }
    }
    
    return filledData;
  };
  
  // Calculate interval based on time range
  const calculateIdealInterval = (days) => {
    if (days <= 1) {
      return 1; // 1 minute for 1 day
    } else if (days <= 7) {
      return 10; // 10 minutes for 1 week
    } else {
      return 30; // 30 minutes for 1 month
    }
  };
  
  // Format timestamp for display
  const formatTimestamp = (timestamp) => {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    return date.toLocaleString();
  };
  
  // Format bit rate for display
  const formatBitRate = (bps) => {
    if (!bps && bps !== 0) return 'N/A';
    
    const numBps = Number(bps);
    if (isNaN(numBps)) return 'N/A';
    
    if (numBps >= 1000000000) {
      return `${(numBps / 1000000000).toFixed(2)} Gbps`;
    } else if (numBps >= 1000000) {
      return `${(numBps / 1000000).toFixed(2)} Mbps`;
    } else if (numBps >= 1000) {
      return `${(numBps / 1000).toFixed(2)} Kbps`;
    }
    return `${numBps.toFixed(2)} bps`;
  };
  
  // Format numbers with K, M, etc.
  const formatNumber = (value) => {
    if (!value && value !== 0) return 'N/A';
    
    const num = Number(value);
    if (isNaN(num)) return 'N/A';
    
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(2)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(2)}K`;
    }
    return num.toFixed(2);
  };
  
  // Format Y-axis labels for bit rates
  const formatBpsAxis = (value) => {
    if (value >= 1000000000) {
      return `${(value / 1000000000).toFixed(1)}G`;
    } else if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`;
    }
    return value;
  };
  
  // Format X-axis timestamps
  const formatXAxis = (timestamp) => {
    if (!timestamp) return '';
    
    // If it's a number, convert it to a Date object
    const date = typeof timestamp === 'number' ? new Date(timestamp) : new Date(timestamp);
    
    if (timeRange <= 1) {
      // For 1 day view, show hours and minutes
      return `${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
    } else if (timeRange <= 7) {
      // For 7 day view, show day of week and hour
      const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
      return `${days[date.getDay()]} ${String(date.getHours()).padStart(2, '0')}h`;
    } else {
      // For longer ranges, show month/day
      return `${date.getMonth() + 1}/${date.getDate()}`;
    }
  };
  
  // Custom tooltip for charts - shows both bandwidth and packet info
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      
      return (
        <div className="bg-white p-3 border border-gray-200 shadow-lg rounded-md text-xs max-w-xs">
          <div className="font-medium mb-2">{data.formattedTime || formatTimestamp(data.timestamp)}</div>
          
          <div className="space-y-4">
            {/* Bandwidth section */}
            <div>
              <div className="font-medium text-gray-600 border-b pb-1 mb-1.5">Bandwidth</div>
              <div className="grid grid-cols-2 gap-2">
                <div className="flex items-center">
                  <ArrowDown className="w-3 h-3 mr-1" style={{ color: colors.inbound }} />
                  <span>Inbound:</span>
                </div>
                <span className="font-medium text-right" style={{ color: colors.inbound }}>
                  {data.in_bps_formatted || formatBitRate(data.in_bps)}
                </span>
                
                <div className="flex items-center">
                  <ArrowUp className="w-3 h-3 mr-1" style={{ color: colors.outbound }} />
                  <span>Outbound:</span>
                </div>
                <span className="font-medium text-right" style={{ color: colors.outbound }}>
                  {data.out_bps_formatted || formatBitRate(data.out_bps)}
                </span>
              </div>
            </div>
            
            {/* Packet section */}
            <div>
              <div className="font-medium text-gray-600 border-b pb-1 mb-1.5">Packets</div>
              <div className="grid grid-cols-2 gap-2">
                <div className="flex items-center">
                  <ArrowDown className="w-3 h-3 mr-1" style={{ color: colors.inPackets }} />
                  <span>Inbound:</span>
                </div>
                <span className="font-medium text-right" style={{ color: colors.inPackets }}>
                  {data.in_pps_formatted || formatNumber(data.in_pps)} pps
                </span>
                
                <div className="flex items-center">
                  <ArrowUp className="w-3 h-3 mr-1" style={{ color: colors.outPackets }} />
                  <span>Outbound:</span>
                </div>
                <span className="font-medium text-right" style={{ color: colors.outPackets }}>
                  {data.out_pps_formatted || formatNumber(data.out_pps)} pps
                </span>
              </div>
            </div>
          </div>
        </div>
      );
    }
    
    return null;
  };
  
  // Export data as CSV
  const exportCSV = () => {
    if (!trafficData || trafficData.length === 0) return;
    
    const headers = [
      'timestamp', 'in_bps', 'out_bps', 'in_pps', 'out_pps', 
      'in_errors', 'out_errors', 'in_bytes', 'out_bytes'
    ];
    
    const csvContent = [
      headers.join(','),
      ...trafficData.map(row => 
        headers.map(header => {
          const value = row[header];
          return value !== undefined ? value : '';
        }).join(',')
      )
    ].join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `port_${port_id}_traffic_${new Date().toISOString().split('T')[0]}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  
  // Calculate statistics
  const calculateStats = () => {
    if (!trafficData || trafficData.length === 0) return null;
    
    const maxInBps = Math.max(...trafficData.map(d => d.in_bps || 0));
    const maxOutBps = Math.max(...trafficData.map(d => d.out_bps || 0));
    const avgInBps = trafficData.reduce((sum, d) => sum + (d.in_bps || 0), 0) / trafficData.length;
    const avgOutBps = trafficData.reduce((sum, d) => sum + (d.out_bps || 0), 0) / trafficData.length;
    
    const maxInPps = Math.max(...trafficData.map(d => d.in_pps || 0));
    const maxOutPps = Math.max(...trafficData.map(d => d.out_pps || 0));
    
    // Get the last data point for current values
    const lastPoint = trafficData[trafficData.length - 1] || {};
    
    return {
      maxInBps,
      maxOutBps,
      avgInBps,
      avgOutBps,
      maxInPps,
      maxOutPps,
      currentInBps: lastPoint.in_bps || 0,
      currentOutBps: lastPoint.out_bps || 0,
      currentInPps: lastPoint.in_pps || 0,
      currentOutPps: lastPoint.out_pps || 0
    };
  };
  
  // Handle brush change (zoom)
  const handleBrushChange = useCallback((brushData) => {
    if (brushData && brushData.startIndex !== undefined && brushData.endIndex !== undefined) {
      // Only update if we have data and valid indices
      if (trafficData.length > 0) {
        const startIndex = Math.max(0, brushData.startIndex);
        const endIndex = Math.min(trafficData.length - 1, brushData.endIndex);
        
        if (startIndex < endIndex) {
          setVisibleDomain([
            trafficData[startIndex].timestamp,
            trafficData[endIndex].timestamp
          ]);
        }
      }
    }
  }, [trafficData]);
  
  // Reset zoom to show full range
  const resetZoom = () => {
    setVisibleDomain(null);
  };
  
  // Get statistics
  const stats = calculateStats();
  
  // Render loading state
  const renderLoading = () => (
    <div className="flex items-center justify-center h-48">
      <div className="text-center">
        <RefreshCw className="w-8 h-8 text-indigo-500 animate-spin mx-auto mb-3" />
        <p className="text-gray-600">Loading traffic data...</p>
      </div>
    </div>
  );
  
  // Render error state
  const renderError = () => (
    <div className="flex items-center justify-center h-48">
      <div className="text-center">
        <AlertTriangle className="w-8 h-8 text-red-500 mx-auto mb-3" />
        <p className="text-red-600 font-medium mb-2">Error Loading Data</p>
        <p className="text-sm text-gray-600 max-w-md">{error}</p>
        <button 
          className="mt-3 px-4 py-2 bg-indigo-50 text-indigo-600 rounded hover:bg-indigo-100 inline-flex items-center text-sm"
          onClick={fetchTrafficData}
        >
          <RefreshCw className="w-4 h-4 mr-2" />
          Retry
        </button>
      </div>
    </div>
  );
  
  // Render empty state
  const renderEmpty = () => (
    <div className="flex items-center justify-center h-48">
      <div className="text-center">
        <Calendar className="w-8 h-8 text-gray-400 mx-auto mb-3" />
        <p className="text-gray-600 font-medium mb-2">No Traffic Data</p>
        <p className="text-sm text-gray-500">No data was found for this time period.</p>
        {timeRange === 1 && (
          <button 
            className="mt-3 px-4 py-2 bg-indigo-50 text-indigo-600 rounded hover:bg-indigo-100 inline-flex items-center text-sm"
            onClick={() => setTimeRange(7)}
          >
            Try 7-Day View
          </button>
        )}
      </div>
    </div>
  );
  
  // Calculate domain for the X-axis
  const calculateXAxisDomain = () => {
    // If zoom is active, use the visible domain
    if (visibleDomain) {
      return visibleDomain;
    }
    
    // Default domain based on time range
    if (trafficData.length > 0) {
      // Find latest timestamp in the data
      const latestTimestamp = Math.max(...trafficData.map(d => d.timestamp));
      
      // Calculate the start of the time range based on the selected view
      const rangeStartDate = new Date(latestTimestamp);
      if (timeRange === 1) {
        rangeStartDate.setHours(rangeStartDate.getHours() - 24); // 24 hours
      } else if (timeRange === 7) {
        rangeStartDate.setDate(rangeStartDate.getDate() - 7); // 7 days
      } else if (timeRange === 30) {
        rangeStartDate.setDate(rangeStartDate.getDate() - 30); // 30 days
      }
      
      return [rangeStartDate.getTime(), latestTimestamp];
    } else {
      // Default to current time minus the selected time range if no data
      const now = new Date();
      const rangeStartDate = new Date(now);
      
      if (timeRange === 1) {
        rangeStartDate.setHours(rangeStartDate.getHours() - 24); // 24 hours
      } else if (timeRange === 7) {
        rangeStartDate.setDate(rangeStartDate.getDate() - 7); // 7 days
      } else if (timeRange === 30) {
        rangeStartDate.setDate(rangeStartDate.getDate() - 30); // 30 days
      }
      
      return [rangeStartDate.getTime(), now.getTime()];
    }
  };
  
  // Render combined chart (bandwidth with packet data on hover)
  const renderCombinedChart = () => {
    const xAxisDomain = calculateXAxisDomain();
    
    return (
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart
          data={trafficData}
          margin={{ top: 5, right: 5, left: 5, bottom: 5 }}
        >
          <defs>
            <linearGradient id="inboundGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor={colors.inbound} stopOpacity={0.8}/>
              <stop offset="95%" stopColor={colors.inbound} stopOpacity={0.1}/>
            </linearGradient>
            <linearGradient id="outboundGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor={colors.outbound} stopOpacity={0.8}/>
              <stop offset="95%" stopColor={colors.outbound} stopOpacity={0.1}/>
            </linearGradient>
          </defs>
          <CartesianGrid strokeDasharray="3 3" stroke={colors.grid} />
          <XAxis 
            dataKey="timestamp" 
            tickFormatter={formatXAxis} 
            tick={{ fontSize: 10, fill: '#6b7280' }}
            axisLine={{ stroke: colors.grid }}
            tickLine={{ stroke: colors.grid }}
            domain={xAxisDomain}
            type="number"
            scale="time"
            allowDataOverflow={true}
          />
          <YAxis 
            tickFormatter={formatBpsAxis} 
            tick={{ fontSize: 10, fill: '#6b7280' }}
            axisLine={{ stroke: colors.grid }}
            tickLine={{ stroke: colors.grid }}
          />
          <Tooltip content={<CustomTooltip />} />
          <Legend iconType="circle" iconSize={8} />
          <Area 
            type="monotone" 
            dataKey="in_bps" 
            name="Inbound" 
            stroke={colors.inbound} 
            fillOpacity={1} 
            fill="url(#inboundGradient)" 
            activeDot={{ r: 5 }} 
            isAnimationActive={!refreshing}
          />
          <Area 
            type="monotone" 
            dataKey="out_bps" 
            name="Outbound" 
            stroke={colors.outbound} 
            fillOpacity={1} 
            fill="url(#outboundGradient)" 
            activeDot={{ r: 5 }} 
            isAnimationActive={!refreshing}
          />
          <Brush 
            dataKey="timestamp" 
            height={20} 
            stroke={colors.inbound} 
            tickFormatter={formatXAxis}
            fill={colors.background}
            onChange={handleBrushChange}
          />
        </AreaChart>
      </ResponsiveContainer>
    );
  };
  
  // Main render
  return (
    <div className="bg-white rounded-lg shadow-sm overflow-hidden">
      {/* Header */}
      <div className="p-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white flex justify-between items-center">
        <div className="text-sm font-medium">Port Traffic Monitor {trafficData.length > 0 && `(${trafficData.length} points)`}</div>
        
        <div className="flex items-center space-x-2">
          {/* Time range selector */}
          <div className="flex rounded-md overflow-hidden text-xs border border-white/30">
            <button
              className={`px-2 py-1 ${
                timeRange === 1 ? 'bg-white text-indigo-700 font-medium' : 'text-white hover:bg-white/10'
              }`}
              onClick={() => setTimeRange(1)}
            >
              1d
            </button>
            <button
              className={`px-2 py-1 ${
                timeRange === 7 ? 'bg-white text-indigo-700 font-medium' : 'text-white hover:bg-white/10'
              }`}
              onClick={() => setTimeRange(7)}
            >
              7d
            </button>
            <button
              className={`px-2 py-1 ${
                timeRange === 30 ? 'bg-white text-indigo-700 font-medium' : 'text-white hover:bg-white/10'
              }`}
              onClick={() => setTimeRange(30)}
            >
              30d
            </button>
          </div>
          
          {/* Action buttons */}
          <div className="flex items-center space-x-1">
            <button
              className="p-1.5 rounded-md bg-white/10 text-white hover:bg-white/20 transition"
              onClick={fetchTrafficData}
              disabled={loading}
              title="Refresh data"
            >
              <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
            </button>
            <button
              className="p-1.5 rounded-md bg-white/10 text-white hover:bg-white/20 transition"
              onClick={exportCSV}
              disabled={loading || !trafficData.length}
              title="Export as CSV"
            >
              <Download className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
      
      {/* Stats section */}
      {!loading && !error && trafficData.length > 0 && stats && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-3 bg-gray-50 border-b">
          {/* Bandwidth stats */}
          <div className="bg-white p-3 rounded-lg border border-gray-100 shadow-sm">
            <div className="text-xs text-gray-500 mb-1">Current Traffic</div>
            <div className="flex items-center justify-between">
              <div className="flex flex-col">
                <div className="flex items-center text-xs">
                  <ArrowDown className="w-3 h-3 mr-1 text-indigo-600" />
                  <span className="text-gray-600">In</span>
                </div>
                <div className="font-semibold text-indigo-600">
                  {formatBitRate(stats.currentInBps)}
                </div>
              </div>
              <div className="flex flex-col items-end">
                <div className="flex items-center text-xs">
                  <ArrowUp className="w-3 h-3 mr-1 text-red-500" />
                  <span className="text-gray-600">Out</span>
                </div>
                <div className="font-semibold text-red-500">
                  {formatBitRate(stats.currentOutBps)}
                </div>
              </div>
            </div>
          </div>
          
          {/* Packet stats */}
          <div className="bg-white p-3 rounded-lg border border-gray-100 shadow-sm">
            <div className="text-xs text-gray-500 mb-1">Current Packets</div>
            <div className="flex items-center justify-between">
              <div className="flex flex-col">
                <div className="flex items-center text-xs">
                  <ArrowDown className="w-3 h-3 mr-1 text-sky-500" />
                  <span className="text-gray-600">In</span>
                </div>
                <div className="font-semibold text-sky-500">
                  {formatNumber(stats.currentInPps)} pps
                </div>
              </div>
              <div className="flex flex-col items-end">
                <div className="flex items-center text-xs">
                  <ArrowUp className="w-3 h-3 mr-1 text-orange-500" />
                  <span className="text-gray-600">Out</span>
                </div>
                <div className="font-semibold text-orange-500">
                  {formatNumber(stats.currentOutPps)} pps
                </div>
              </div>
            </div>
          </div>
          
          {/* Max bandwidth */}
          <div className="bg-white p-3 rounded-lg border border-gray-100 shadow-sm">
            <div className="text-xs text-gray-500 mb-1">Max Bandwidth</div>
            <div className="flex flex-col">
              <div className="flex items-center justify-between text-xs">
                <div className="flex items-center">
                  <span className="w-2 h-2 rounded-full bg-indigo-600 mr-1.5" />
                  <span className="text-gray-600">In:</span>
                </div>
                <span className="font-medium text-indigo-600">{formatBitRate(stats.maxInBps)}</span>
              </div>
              <div className="flex items-center justify-between text-xs mt-1">
                <div className="flex items-center">
                  <span className="w-2 h-2 rounded-full bg-red-500 mr-1.5" />
                  <span className="text-gray-600">Out:</span>
                </div>
                <span className="font-medium text-red-500">{formatBitRate(stats.maxOutBps)}</span>
              </div>
            </div>
          </div>
          
          {/* Max packets */}
          <div className="bg-white p-3 rounded-lg border border-gray-100 shadow-sm">
            <div className="text-xs text-gray-500 mb-1">Max Packet Rate</div>
            <div className="flex flex-col">
              <div className="flex items-center justify-between text-xs">
                <div className="flex items-center">
                  <span className="w-2 h-2 rounded-full bg-sky-500 mr-1.5" />
                  <span className="text-gray-600">In:</span>
                </div>
                <span className="font-medium text-sky-500">{formatNumber(stats.maxInPps)} pps</span>
              </div>
              <div className="flex items-center justify-between text-xs mt-1">
                <div className="flex items-center">
                  <span className="w-2 h-2 rounded-full bg-orange-500 mr-1.5" />
                  <span className="text-gray-600">Out:</span>
                </div>
                <span className="font-medium text-orange-500">{formatNumber(stats.maxOutPps)} pps</span>
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* Chart container - Combined */}
      <div className="h-60 relative p-2">
        {loading && !refreshing ? renderLoading() : 
         error ? renderError() : 
         trafficData.length === 0 ? renderEmpty() : renderCombinedChart()}
         
        {/* Overlay for refreshing state */}
        {refreshing && (
          <div className="absolute inset-0 bg-white bg-opacity-70 flex items-center justify-center z-10">
            <div className="flex items-center px-3 py-1.5 bg-indigo-100 text-indigo-700 rounded-md shadow-sm">
              <RefreshCw className="w-4 h-4 animate-spin mr-2" />
              <span className="text-sm font-medium">Refreshing...</span>
            </div>
          </div>
        )}
      </div>
      
      {/* Footer */}
      {!loading && !error && trafficData.length > 0 && (
        <div className="p-2 border-t bg-gray-50 flex justify-between items-center text-xs text-gray-500">
          <div className="flex items-center">
            <Clock className="w-3 h-3 mr-1.5" />
            Updated {getLastUpdatedText()}
          </div>
          <div className="flex items-center">
            {trafficData.length} data points {trafficData.length > 1 ? 
              visibleDomain ? 
                `(zoomed view)` : 
                `(showing full ${timeRange === 1 ? '24 hours' : timeRange === 7 ? '7 days' : '30 days'})` : 
              `over ${timeRange} day${timeRange !== 1 ? 's' : ''}`}
            
            {/* Reset zoom button */}
            {visibleDomain && (
              <button
                className="ml-2 px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
                onClick={resetZoom}
              >
                Reset Zoom
              </button>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default PortTrafficGraph;